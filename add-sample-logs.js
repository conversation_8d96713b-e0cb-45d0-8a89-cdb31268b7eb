// Simple script to add sample logs directly to MongoDB
const { MongoClient } = require('mongodb');

const uri = 'mongodb://localhost:27017';
const dbName = 'aslaa';

async function addSampleLogs() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection('logs');
    
    // Sample log entries
    const sampleLogs = [
      {
        user: 'user1',
        userId: 'user1',
        deviceNumber: '12345',
        command: 'POWER_ON',
        commandType: 'power_on',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        receiveTime: new Date(Date.now() - 1000 * 60 * 60 * 2 + 2000),
        responseTime: 2000,
        response: 'OK',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample power on command',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2 + 2000)
      },
      {
        user: 'user2',
        userId: 'user2',
        deviceNumber: '67890',
        command: 'LOCK',
        commandType: 'lock',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 60 * 1),
        receiveTime: new Date(Date.now() - 1000 * 60 * 60 * 1 + 1500),
        responseTime: 1500,
        response: 'SUCCESS',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample lock command',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 1),
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 1 + 1500)
      },
      {
        user: 'user1',
        userId: 'user1',
        deviceNumber: '12345',
        command: 'GET_STATUS',
        commandType: 'status',
        sent: 'yes',
        success: false,
        sentTime: new Date(Date.now() - 1000 * 60 * 30),
        receiveTime: new Date(Date.now() - 1000 * 60 * 30 + 30000),
        responseTime: 30000,
        response: 'TIMEOUT',
        responseType: 'MQTT',
        deviceOnline: false,
        responseStatus: 'timeout',
        failureReason: 'Device did not respond within timeout period',
        message: 'Sample failed status command',
        createdAt: new Date(Date.now() - 1000 * 60 * 30),
        updatedAt: new Date(Date.now() - 1000 * 60 * 30 + 30000)
      },
      {
        user: 'user3',
        userId: 'user3',
        deviceNumber: '11111',
        command: 'UNLOCK',
        commandType: 'unlock',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 15),
        receiveTime: new Date(Date.now() - 1000 * 60 * 15 + 1200),
        responseTime: 1200,
        response: 'DONE',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample unlock command',
        createdAt: new Date(Date.now() - 1000 * 60 * 15),
        updatedAt: new Date(Date.now() - 1000 * 60 * 15 + 1200)
      },
      {
        user: 'user2',
        userId: 'user2',
        deviceNumber: '67890',
        command: 'GET_LOCATION',
        commandType: 'location',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 5),
        receiveTime: new Date(Date.now() - 1000 * 60 * 5 + 3000),
        responseTime: 3000,
        response: 'OK',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample location command',
        createdAt: new Date(Date.now() - 1000 * 60 * 5),
        updatedAt: new Date(Date.now() - 1000 * 60 * 5 + 3000)
      }
    ];
    
    // Remove existing sample data
    await collection.deleteMany({ message: { $regex: /^Sample/ } });
    console.log('Removed existing sample data');
    
    // Insert new sample data
    const result = await collection.insertMany(sampleLogs);
    console.log(`Inserted ${result.insertedCount} sample log entries`);
    
    // Verify the data
    const count = await collection.countDocuments({ message: { $regex: /^Sample/ } });
    console.log(`Total sample logs in database: ${count}`);
    
    // Show some statistics
    const stats = await collection.aggregate([
      { $match: { message: { $regex: /^Sample/ } } },
      {
        $group: {
          _id: null,
          totalCommands: { $sum: 1 },
          successfulCommands: { $sum: { $cond: ['$success', 1, 0] } },
          failedCommands: { $sum: { $cond: ['$success', 0, 1] } },
          avgResponseTime: { $avg: '$responseTime' },
          uniqueDevices: { $addToSet: '$deviceNumber' },
          uniqueUsers: { $addToSet: '$userId' }
        }
      }
    ]).toArray();
    
    if (stats.length > 0) {
      const stat = stats[0];
      console.log('\n📊 Sample Data Summary:');
      console.log(`Total Commands: ${stat.totalCommands}`);
      console.log(`Successful: ${stat.successfulCommands}`);
      console.log(`Failed: ${stat.failedCommands}`);
      console.log(`Success Rate: ${((stat.successfulCommands / stat.totalCommands) * 100).toFixed(1)}%`);
      console.log(`Average Response Time: ${stat.avgResponseTime.toFixed(0)}ms`);
      console.log(`Unique Devices: ${stat.uniqueDevices.length}`);
      console.log(`Unique Users: ${stat.uniqueUsers.length}`);
    }
    
    console.log('\n✅ Sample data added successfully!');
    console.log('You can now refresh the statistics dashboard to see the data.');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

addSampleLogs();
