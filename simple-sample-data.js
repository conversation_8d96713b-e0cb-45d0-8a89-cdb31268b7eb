// Simple script to add sample data via API
const axios = require('axios');

const createSampleData = async () => {
  try {
    console.log('Creating sample log entries...');

    // Sample data
    const sampleLogs = [
      {
        user: 'user1',
        userId: 'user1',
        deviceNumber: '12345',
        command: 'POWER_ON',
        commandType: 'power_on',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        receiveTime: new Date(Date.now() - 1000 * 60 * 60 * 2 + 2000), // 2 seconds later
        responseTime: 2000,
        response: 'OK',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample power on command'
      },
      {
        user: 'user2',
        userId: 'user2',
        deviceNumber: '67890',
        command: 'LOCK',
        commandType: 'lock',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 60 * 1), // 1 hour ago
        receiveTime: new Date(Date.now() - 1000 * 60 * 60 * 1 + 1500),
        responseTime: 1500,
        response: 'SUCCESS',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample lock command'
      },
      {
        user: 'user1',
        userId: 'user1',
        deviceNumber: '12345',
        command: 'GET_STATUS',
        commandType: 'status',
        sent: 'yes',
        success: false,
        sentTime: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        receiveTime: new Date(Date.now() - 1000 * 60 * 30 + 30000),
        responseTime: 30000,
        response: 'TIMEOUT',
        responseType: 'MQTT',
        deviceOnline: false,
        responseStatus: 'timeout',
        failureReason: 'Device did not respond within timeout period',
        message: 'Sample failed status command'
      }
    ];

    console.log('Sample data created in memory. You can manually insert this into MongoDB.');
    console.log('Sample logs:', JSON.stringify(sampleLogs, null, 2));

  } catch (error) {
    console.error('Error:', error);
  }
};

createSampleData();
