const mongoose = require("mongoose");
const Schema = mongoose.Schema;

// Create Enhanced Schema for Command Statistics
const LogSchema = new Schema({
    user: {
        type: String,
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    deviceNumber: {
        type: String,
        default: ""
    },
    deviceType: {
        type: String,
        default: '4g',
    },
    command: {
        type: String,
        default: ""
    },
    commandType: {
        type: String,
        enum: ['power_on', 'power_off', 'lock', 'unlock', 'location', 'status', 'config', 'other'],
        default: 'other'
    },
    sent: {
        type: String,
        default: "No"
    },
    success: {
        type: Boolean,
        default: false
    },
    response: {
        type: String,
        default: "Response Message"
    },
    responseStatus: {
        type: String,
        enum: ['success', 'failed', 'timeout', 'device_offline', 'invalid_response', 'pending'],
        default: 'pending'
    },
    failureReason: {
        type: String,
        default: ""
    },
    sentTime: {
        type: Date,
    },
    receiveTime: {
        type: Date,
    },
    responseTime: {
        type: Number, // milliseconds
        default: null
    },
    message: {
        type: String,
        default: ""
    },
    responseType: {
        type: String,
        default: "HTTP"
    },
    deviceOnline: {
        type: Boolean,
        default: true
    },
    signalStrength: {
        type: Number,
        default: null
    },
    createdAt: {
        type: Date,
        default: Date.now,
    }
}, {
    capped: { size: 102400, max: 100000, autoIndexId: true },
    timestamps: true
});

// Add indexes for better query performance
LogSchema.index({ userId: 1, createdAt: -1 });
LogSchema.index({ deviceNumber: 1, createdAt: -1 });
LogSchema.index({ commandType: 1, createdAt: -1 });
LogSchema.index({ responseStatus: 1, createdAt: -1 });
LogSchema.index({ createdAt: -1 });

// Compound indexes for common query patterns
LogSchema.index({ userId: 1, deviceNumber: 1, createdAt: -1 });
LogSchema.index({ success: 1, createdAt: -1 });
LogSchema.index({ commandType: 1, success: 1, createdAt: -1 });
LogSchema.index({ deviceNumber: 1, success: 1, createdAt: -1 });
LogSchema.index({ responseTime: 1, createdAt: -1 });

// Sparse indexes for optional fields
LogSchema.index({ responseTime: 1 }, { sparse: true });
LogSchema.index({ signalStrength: 1 }, { sparse: true });

module.exports = Log = mongoose.model("log", LogSchema);