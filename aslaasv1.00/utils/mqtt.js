const mqtt = require("mqtt");
const device = require("../models/device");
const GpsLogModel = require("../models/gpslog");
const carGps = require("../models/carGps");
const { sendMessageToChannel, emitToAdmins } = require("./socket");
const DeviceMessageLogModel = require("../models/deviceMessageLog");
const MopedMessageLogModel = require("../models/mopedMessageLog");
const SimcardSmsLogModel = require("../models/simSmsLog");
const firebaseAdmin = require("firebase-admin");
const LogModel = require("../models/log");
const UserModel = require("../models/user");
const UserActivityStatistics = require("../models/userActivityStatistics");

const url = "mqtt://45.76.188.38:1883";
// const url = "mqtt://127.0.0.1:1883";
const voltFlag = {}; // This will store the state of the volt for each device

let client = null;

const messaging = firebaseAdmin.messaging();

const REQUIRED_DEVICE_LENGTH = 15; // The required length for device numbers

// Update user activity statistics in real-time
const updateUserActivity = async (userId, commandType, deviceNumber, deviceDoc) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Update daily user activity statistics - robust approach with conflict handling
    try {
      // First, try to find existing document
      let existingStats = await UserActivityStatistics.findOne({
        userId: userId,
        period: 'daily',
        date: today
      });

      if (existingStats) {
        try {
          // Try to update existing document with simple increment
          existingStats.commandStats.totalCommands += 1;
          existingStats.commandTypes[commandType] = (existingStats.commandTypes[commandType] || 0) + 1;
          existingStats.lastActivity = new Date();
          existingStats.updatedAt = new Date();
          await existingStats.save();
          console.log(`✅ Updated existing user activity stats for user ${userId}: totalCommands=${existingStats.commandStats.totalCommands}, ${commandType}=${existingStats.commandTypes[commandType]}`);
        } catch (saveError) {
          // If save fails due to corruption, delete and recreate
          console.log(`⚠️ Corrupted document detected for user ${userId}, recreating...`);
          await UserActivityStatistics.deleteOne({ _id: existingStats._id });

          // Create new document
          const newStats = new UserActivityStatistics({
            userId: userId,
            period: 'daily',
            date: today,
            phoneNumber: deviceDoc.phoneNumber || 'unknown',
            commandStats: {
              totalCommands: 1,
              successfulCommands: 0,
              failedCommands: 0,
              successRate: 0
            },
            commandTypes: {
              power_on: commandType === 'power_on' ? 1 : 0,
              power_off: commandType === 'power_off' ? 1 : 0,
              lock: commandType === 'lock' ? 1 : 0,
              unlock: commandType === 'unlock' ? 1 : 0,
              location: commandType === 'location' ? 1 : 0,
              status: commandType === 'status' ? 1 : 0,
              config: commandType === 'config' ? 1 : 0,
              other: commandType === 'other' ? 1 : 0
            },
            deviceUsage: {
              devicesUsed: 1,
              primaryDevice: deviceNumber,
              deviceSwitches: 0
            },
            responseTimes: {
              avgResponseTime: null,
              fastestResponse: null,
              slowestResponse: null
            },
            activityPattern: {
              mostActiveHour: null,
              leastActiveHour: null,
              weekdayActivity: 0,
              weekendActivity: 0
            },
            lastActivity: new Date(),
            updatedAt: new Date()
          });
          await newStats.save();
          console.log(`✅ Recreated user activity stats for user ${userId}: totalCommands=1`);
        }
      } else {
        // Create new document
        const newStats = new UserActivityStatistics({
          userId: userId,
          period: 'daily',
          date: today,
          phoneNumber: deviceDoc.phoneNumber || 'unknown',
          commandStats: {
            totalCommands: 1,
            successfulCommands: 0,
            failedCommands: 0,
            successRate: 0
          },
          commandTypes: {
            power_on: commandType === 'power_on' ? 1 : 0,
            power_off: commandType === 'power_off' ? 1 : 0,
            lock: commandType === 'lock' ? 1 : 0,
            unlock: commandType === 'unlock' ? 1 : 0,
            location: commandType === 'location' ? 1 : 0,
            status: commandType === 'status' ? 1 : 0,
            config: commandType === 'config' ? 1 : 0,
            other: commandType === 'other' ? 1 : 0
          },
          deviceUsage: {
            devicesUsed: 1,
            primaryDevice: deviceNumber,
            deviceSwitches: 0
          },
          responseTimes: {
            avgResponseTime: null,
            fastestResponse: null,
            slowestResponse: null
          },
          activityPattern: {
            mostActiveHour: null,
            leastActiveHour: null,
            weekdayActivity: 0,
            weekendActivity: 0
          },
          lastActivity: new Date(),
          updatedAt: new Date()
        });
        await newStats.save();
        console.log(`✅ Created new user activity stats for user ${userId}: totalCommands=1`);
      }
    } catch (error) {
      console.error('Error updating user activity:', error);
    }

    console.log(`User activity updated for user ${userId}: ${commandType} command on device ${deviceNumber}`);
  } catch (error) {
    console.error('Error updating user activity:', error);
  }
};

// Update user activity with response information
const updateUserActivityResponse = async (userId, success, responseTime, commandType) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Update daily user activity statistics with response data
    const updateData = {
      $set: {
        lastActivity: new Date(),
        updatedAt: new Date()
      }
    };

    // Increment success or failure counters
    if (success) {
      updateData.$inc = { 'commandStats.successfulCommands': 1 };
    } else {
      updateData.$inc = { 'commandStats.failedCommands': 1 };
    }

    // Update response times if valid
    if (responseTime && responseTime > 0) {
      const userStats = await UserActivityStatistics.findOne({
        userId: userId,
        period: 'daily',
        date: today
      });

      if (userStats) {
        const currentAvg = userStats.responseTimes.avgResponseTime || 0;
        const totalCommands = userStats.commandStats.totalCommands || 1;
        const newAvg = ((currentAvg * (totalCommands - 1)) + responseTime) / totalCommands;

        updateData.$set['responseTimes.avgResponseTime'] = newAvg;

        // Update fastest response
        if (!userStats.responseTimes.fastestResponse || responseTime < userStats.responseTimes.fastestResponse) {
          updateData.$set['responseTimes.fastestResponse'] = responseTime;
        }

        // Update slowest response
        if (!userStats.responseTimes.slowestResponse || responseTime > userStats.responseTimes.slowestResponse) {
          updateData.$set['responseTimes.slowestResponse'] = responseTime;
        }

        // Calculate success rate
        const successfulCommands = (userStats.commandStats.successfulCommands || 0) + (success ? 1 : 0);
        const successRate = (successfulCommands / totalCommands) * 100;
        updateData.$set['commandStats.successRate'] = successRate;
      }
    }

    await UserActivityStatistics.findOneAndUpdate(
      {
        userId: userId,
        period: 'daily',
        date: today
      },
      updateData,
      { upsert: true, new: true }
    );

    console.log(`User activity response updated for user ${userId}: success=${success}, responseTime=${responseTime}ms`);
  } catch (error) {
    console.error('Error updating user activity response:', error);
  }
};

// Function to log commands to statistics
const logCommandToStatistics = async (deviceNumber, command, method = 'MQTT_DIRECT') => {
  try {
    // Find device to get user information
    const deviceDoc = await device.findOne({ deviceNumber });
    if (!deviceDoc) {
      console.log(`Device not found for command logging: ${deviceNumber}`);
      return;
    }

    // Determine command type
    let commandType = 'other';
    const cmd = command.toLowerCase();
    if (cmd.includes('lock') || cmd === 'lock') commandType = 'lock';
    else if (cmd.includes('unlock') || cmd === 'unlock') commandType = 'unlock';
    else if (cmd.includes('engine') || cmd === 'engine') commandType = 'power_on';
    else if (cmd.includes('location') || cmd === 'check') commandType = 'location';

    // Create log entry using the Log model
    const logData = {
      user: deviceDoc.userId ? deviceDoc.userId.toString() : 'unknown',
      userId: deviceDoc.userId,
      deviceNumber,
      deviceType: deviceDoc.type || '4g',
      command,
      commandType,
      sent: 'yes',
      success: null, // Will be updated when response is received
      sentTime: new Date(),
      responseType: method,
      deviceOnline: true,
      message: `Command captured via ${method}`
    };

    const commandLog = new LogModel(logData);
    await commandLog.save();

    console.log(`Command logged to database: ${deviceNumber} - ${command} (${commandType})`);

    // Update user activity statistics in real-time
    // Find user by device phoneNumber since devices don't have userId field
    let userId = null;
    if (deviceDoc.phoneNumber) {
      const userDoc = await UserModel.findOne({ phoneNumber: deviceDoc.phoneNumber });
      if (userDoc) {
        userId = userDoc._id;
        console.log(`🔥 Found user ${userId} for device ${deviceNumber} (phoneNumber: ${deviceDoc.phoneNumber})`);
        await updateUserActivity(userId, commandType, deviceNumber, deviceDoc);
      } else {
        console.log(`⚠️ No user found for device ${deviceNumber} with phoneNumber: ${deviceDoc.phoneNumber}`);
      }
    } else {
      console.log(`⚠️ No phoneNumber found for device ${deviceNumber} - cannot find user`);
    }

    // Emit real-time statistics update via Socket.IO
    emitToAdmins('statistics-command-update', {
      type: 'command_logged',
      deviceNumber,
      command,
      commandType,
      timestamp: new Date(),
      userId: userId,
      phoneNumber: deviceDoc.phoneNumber
    });
  } catch (error) {
    console.error('Error logging command to database:', error);
  }
};

const subscribe = (imei) => {
  if (client != null && client.connected) {
    client.subscribe([`${imei}/post`], () => {
      console.log(`subscribe ... ${imei}`);
    });
    return true;
  } else {
    return false;
  }
};

const subscribeCar2 = (imei) => {
  if (client != null && client.connected) {
    client.subscribe([`${imei}/msg`], (err) => {
      if (err) {
        console.log(`❌ Failed to subscribe to ${imei}/msg:`, err);
      } else {
        console.log(`✅ Subscribed to response topic: ${imei}/msg`);
      }
    });
    return true;
  } else {
    console.log(`❌ Cannot subscribe to ${imei}/msg - MQTT client not connected`);
    return false;
  }
};

const subscribeCommands = (imei) => {
  if (client != null && client.connected) {
    client.subscribe([`${imei}`], (err) => {
      if (err) {
        console.log(`❌ Failed to subscribe to ${imei}:`, err);
      } else {
        console.log(`✅ Subscribed to command topic: ${imei}`);
      }
    });
    return true;
  } else {
    console.log(`❌ Cannot subscribe to ${imei} - MQTT client not connected`);
    return false;
  }
};

const unsubscribeCar2 = (imei) => {
  if (client != null && client.connected) {
    client.unsubscribe([`${imei}/msg`], () => {
      console.log(`unsubscribe car2... ${imei}`);
    });
    return true;
  } else {
    return false;
  }
};

const unsubscribe = (imei) => {
  console.log("..unsubscribing...");
  if (client != null && client.connected) {
    client.unsubscribe([`${imei}/post`], () => {
      console.log(`unsubscribe ... ${imei}`);
    });
    return true;
  } else {
    return false;
  }
};

const publish = (data, isHex = true) => {
  if (client != null && client.connected) {
      return new Promise((resolve, reject) => {
          if (isHex) {
              const hex = Buffer.from(data.payload, 'hex');

              client.publish(data.topic, hex.toString('ascii'), { qos: data.qos, retain: data.retain }, (err) => {
                  if (!err) {
                      return resolve('success');
                  } else {
                      return reject('failed');
                  }
              });
          } else {
              try {
                  let payload = `{"id":"${data.topic}","command":"${data.payload}"}`;
                  if (data.timer) {
                      payload = `{"id":"${data.topic}","command":"${data.payload}","timer":${(data.timer)}}`
                  }
                  if (data.temp) {
                      payload = `{"id":"${data.topic}","command":"${data.payload}","temp":"${data.temp[0]}x${data.temp[1]}"}`
                  }
                  client.publish(data.topic, payload, { qos: data.qos, retain: data.retain }, (err) => {
                      if (!err) {
                          return resolve('success');
                      } else {
                          console.log(err)
                          return reject('failed');
                      }
                  });
              } catch (err) {
                  console.log(err)
                  return reject('failed');
              }
              //


          }
      })
  } else {
      return 'failed'
  }

} 

const schedpublish = async (message, retain = false) => {
  return new Promise((resolve, reject) => {
    try {
      if (!client || !client.connected) {
        console.error("MQTT client not connected");
        return resolve("mqtt-not-connected");
      }

      const { topic, payload, qos = 0 } = message;
      
      // Ensure payload is a string
      const stringPayload = typeof payload === 'object' ? JSON.stringify(payload) : payload;
      
      console.log(`Publishing to ${topic}: ${stringPayload}`);
      
      client.publish(topic, stringPayload, { qos, retain }, (err) => {
        if (err) {
          console.error(`Error publishing to ${topic}:`, err);
          return resolve("error");
        }
        
        console.log(`Successfully published to ${topic}`);
        return resolve("success");
      });
    } catch (error) {
      console.error("Error in schedpublish:", error);
      return resolve("error");
    }
  });
};

// Store command response handlers
const commandResponseHandlers = new Map();

// Register a command response handler
const registerCommandResponseHandler = (deviceNumber, handler) => {
  commandResponseHandlers.set(deviceNumber, handler);
  
  // Set a timeout to automatically remove the handler after 15 seconds
  setTimeout(() => {
    if (commandResponseHandlers.has(deviceNumber)) {
      console.log(`Removing stale command response handler for device ${deviceNumber}`);
      commandResponseHandlers.delete(deviceNumber);
    }
  }, 15000);
};

// Remove a command response handler
const removeCommandResponseHandler = (deviceNumber) => {
  commandResponseHandlers.delete(deviceNumber);
};

const initializeMqttClient = () => {
  console.log("...connecting to MQTT.....");
  client = mqtt.connect(url, {});

  client.on("disconnect", () => {
    console.log("disconnected");
  });

  client.on("connect", async () => {
    console.log("connected to MQTT server");
    // Fetch all devices and subscribe to their msg topics
    try {
      const allDevices = await device.find({});
      console.log(`🔍 Found ${allDevices.length} devices in database`);

      let subscribedCount = 0;
      allDevices.forEach((dev) => {
        const imei = dev.deviceNumber;
        if (imei && imei.length === REQUIRED_DEVICE_LENGTH) {
          console.log(`📡 Subscribing to device: ${imei}`);
          subscribeCar2(imei);    // Subscribe to responses (deviceNumber/msg)
          subscribeCommands(imei); // Subscribe to commands (deviceNumber)
          subscribedCount++;
        } else {
          console.log(`⚠️ Skipping device with invalid IMEI: ${imei} (length: ${imei?.length})`);
        }
      });

      console.log(`✅ Successfully subscribed to ${subscribedCount} device topics`);
    } catch (err) {
      console.error("Error subscribing devices:", err);
    }
  });

  const motionFlag = {}; // Flag to track motion for each device
  const speedFlag = {};  // Flag to track speed for each device
  
  client.on("message", async (topic, payload) => {
    console.log(`📨 MQTT message received - Topic: ${topic}, Payload length: ${payload.length}`);

    // Handle device responses (deviceNumber/msg)
    if (topic.toLowerCase().includes("/msg")) {
      const deviceNumber = topic.replace("/msg", "");
      let response = payload.toString();
      console.log(`📱 Device response received from ${deviceNumber}: ${response.substring(0, 100)}...`);
      
      // Sanitize the response (remove unwanted characters, control chars, etc.)
      const sanitizedResponse = response
        .replace(/[\u0000-\u001F\u007F-\u009F\u00AD\u0600-\u0604\u070F\u17B4\u17B5\u200C-\u200F\u2028-\u202F\u2060-\u206F\uFEFF\uFFF0-\uFFFF]/g, '')
        .replace(/\\n/g, "")
        .replace(/[\r\n]+$/, '')
        .trim();
      
      // Check if there's a registered command response handler for this device
      const hasCommandHandler = commandResponseHandlers.has(deviceNumber);
      
      // If there's a command handler, call it with the response
      if (hasCommandHandler) {
        console.log(`Found command response handler for device ${deviceNumber}`);
        const handler = commandResponseHandlers.get(deviceNumber);

        try {
          // Parse the response for the command handler
          let parsedResponse;
          try {
            parsedResponse = JSON.parse(sanitizedResponse);
          } catch (e) {
            parsedResponse = { rawPayload: sanitizedResponse };
          }

          // Call the handler with the parsed response
          handler(parsedResponse);

          // Remove the handler after it's been called
          commandResponseHandlers.delete(deviceNumber);

          console.log(`Command response handler for device ${deviceNumber} executed and removed`);
        } catch (err) {
          console.error(`Error executing command response handler for device ${deviceNumber}:`, err);
        }
      }

      // Log response for statistics (for all device responses)
      try {
        const LogModel = require("../models/log");

        // Find the most recent pending command for this device
        console.log(`🔍 Looking for pending command for device ${deviceNumber}...`);
        const recentLog = await LogModel.findOne({
          deviceNumber: deviceNumber,
          success: null, // Still pending
          sentTime: { $gte: new Date(Date.now() - 60000) } // Within last minute
        }).sort({ sentTime: -1 });

        if (recentLog) {
          console.log(`🔗 Found pending command for device ${deviceNumber}: ${recentLog.command} (${recentLog.commandType}) sent at ${recentLog.sentTime}`);
          const responseTime = Date.now() - recentLog.sentTime.getTime();
          const success = !sanitizedResponse.toLowerCase().includes('error') &&
                         !sanitizedResponse.toLowerCase().includes('timeout') &&
                         sanitizedResponse.length > 0;

          // Update the log with response information
          try {
            await LogModel.findByIdAndUpdate(recentLog._id, {
              success: success,
              receiveTime: new Date(),
              responseTime: responseTime,
              responseStatus: success ? 'success' : 'failed'
            });
            console.log(`✅ Successfully updated log for device ${deviceNumber}`);
          } catch (updateError) {
            if (updateError.message.includes('capped collection')) {
              console.log(`⚠️ Capped collection detected - creating new log entry instead of updating`);
              // For capped collections, create a new response log entry
              const responseLog = new LogModel({
                deviceNumber,
                command: `${recentLog.command}_response`,
                commandType: 'response',
                success: success,
                sentTime: recentLog.sentTime,
                receiveTime: new Date(),
                responseTime: responseTime,
                response: sanitizedResponse.substring(0, 100), // Shorter response
                responseStatus: success ? 'success' : 'failed',
                deviceOnline: true,
                message: `Response to ${recentLog.command}`
              });
              await responseLog.save();
            } else {
              throw updateError; // Re-throw if it's a different error
            }
          }

          // Update user activity with response information
          // Find user by device phoneNumber since logs don't have userId field
          let responseUserId = null;
          const responseDeviceDoc = await device.findOne({ deviceNumber });
          if (responseDeviceDoc && responseDeviceDoc.phoneNumber) {
            const responseUserDoc = await UserModel.findOne({ phoneNumber: responseDeviceDoc.phoneNumber });
            if (responseUserDoc) {
              responseUserId = responseUserDoc._id;
              console.log(`🔥 Found user ${responseUserId} for response tracking on device ${deviceNumber} (phoneNumber: ${responseDeviceDoc.phoneNumber})`);
              await updateUserActivityResponse(responseUserId, success, responseTime, recentLog.commandType);
              console.log(`✅ User activity response updated for user ${responseUserId}: success=${success}, responseTime=${responseTime}ms`);
            } else {
              console.log(`⚠️ No user found for response tracking on device ${deviceNumber} with phoneNumber: ${responseDeviceDoc.phoneNumber}`);
            }
          } else {
            console.log(`⚠️ No phoneNumber found for device ${deviceNumber} - cannot find user for response tracking`);
          }

          console.log(`Statistics: Updated command log for device ${deviceNumber}, success: ${success}, responseTime: ${responseTime}ms`);
        } else {
          console.log(`⚠️ No pending command found for device ${deviceNumber} in the last 60 seconds`);

          // Debug: Check what commands exist for this device
          const allRecentCommands = await LogModel.find({
            deviceNumber: deviceNumber,
            sentTime: { $gte: new Date(Date.now() - 300000) } // Last 5 minutes
          }).sort({ sentTime: -1 }).limit(5);

          console.log(`🔍 Recent commands for ${deviceNumber}:`, allRecentCommands.map(log => ({
            command: log.command,
            sentTime: log.sentTime,
            success: log.success,
            hasResponse: !!log.response
          })));
        }
      } catch (statsError) {
        console.log('Statistics logging error (non-critical):', statsError.message);
      }

      const foundDevice = await device.findOne({ deviceNumber });
      if (!foundDevice) {
        console.log(`Device with number ${deviceNumber} not found`);
        return;
      }
      
      // Try to parse the message as JSON
      try {
        const parsedMessage = JSON.parse(sanitizedResponse);
        
        // Check if the message contains server information
        if (parsedMessage.server && parsedMessage.id === deviceNumber) {
          console.log(`Updating device ${deviceNumber} renter to ${parsedMessage.server}`);
          
          // Update the device's renter field with the server value
          await device.findOneAndUpdate(
            { deviceNumber },
            { renter: parsedMessage.server }
          );
          
          console.log(`Device ${deviceNumber} renter updated to ${parsedMessage.server}`);
        }
      } catch (err) {
        // If parsing fails, continue with normal message processing
        console.log(`Could not parse message as JSON for device ${deviceNumber}`);
      }

      // Create a safe message object that doesn't rely on JSON parsing
      const msgObject = { payload: sanitizedResponse };
      
      if (msgObject && msgObject.payload) {
        try {
          // Try parsing the sanitized payload
          const parsedPayload = JSON.parse(sanitizedResponse);
          const { volt, motion, Speed } = parsedPayload;
          const deviceFlag = voltFlag[deviceNumber];
          const motionDeviceFlag = motionFlag[deviceNumber];
          const speedDeviceFlag = speedFlag[deviceNumber];

          // Process SMS content if present
          if (parsedPayload.sms) {
            console.log(`SMS found for device ${deviceNumber}:`, parsedPayload.sms);
            await handleSmsContent(deviceNumber, parsedPayload.sms);
          }
          
          // Rest of the voltage, motion, and speed handling...
          
        } catch (err) {
          console.error("Error parsing payload for device", deviceNumber, ":", err);
          // Continue with the flow even if parsing fails
        }
      }

      // Handle GPS content with our improved function
      await handleGpsContent(deviceNumber, sanitizedResponse, foundDevice);
      
      // Send message to channel with the raw payload to avoid parsing issues
      // Pass isCommandResponse flag to indicate if this is a command response
      sendMessageToChannel(deviceNumber, msgObject, "4g", hasCommandHandler);
    }
    // Handle device commands (deviceNumber without /msg)
    else if (!topic.includes("/") && topic.length === REQUIRED_DEVICE_LENGTH) {
      const deviceNumber = topic;
      const commandPayload = payload.toString();

      console.log(`Command captured for device ${deviceNumber}: ${commandPayload}`);

      try {
        // Try to parse the command JSON
        const commandData = JSON.parse(commandPayload);

        if (commandData.id && commandData.command) {
          console.log(`Logging command: Device ${commandData.id}, Command: ${commandData.command}`);

          // Log the command to statistics
          await logCommandToStatistics(commandData.id, commandData.command, 'MQTT_DIRECT');
        }
      } catch (parseError) {
        console.log(`Could not parse command JSON for device ${deviceNumber}: ${commandPayload}`);
        // Still try to log the raw command
        await logCommandToStatistics(deviceNumber, commandPayload, 'MQTT_DIRECT');
      }
    }
  });
};

// Helper function to send Firebase messages
const sendMessageFirebase = async (message, fmcToken) => {
  if (!fmcToken) {
    console.log("No FCM token provided, skipping message send.");
    return;
  }

  try {
    const messagePayload = {
      notification: {
        title: "Машин",
        body: message,
      },
      token: fmcToken,
    };
    await messaging.send(messagePayload);
    console.log("Firebase message sent successfully");
  } catch (error) {
    // console.error("Error sending Firebase message:", error);
  }
};

const handleSmsContent = async (deviceNumber, content) => {
  try {
    let model = new SimcardSmsLogModel({
      deviceNumber: deviceNumber,
      content,
    });

    // Attempt to parse numeric balance from the content.
    // Formats: "XXXX.XX TG" or "XXXX tug"
    const balanceRegex = /(\d+(\.\d+)?)(\s*tug|\s*TG)?/i;
    const balanceMatch = content.match(balanceRegex);

    let balance = null;
    if (balanceMatch && balanceMatch[1]) {
      balance = balanceMatch[1];
    }

    // Attempt to parse date from the content.
    // Considering both YYYY/MM/DD and DD-MM-YYYY formats.
    const dateRegexes = [
      /\d{4}\/\d{2}\/\d{2}/,    // YYYY/MM/DD
      /\d{2}-\d{2}-\d{4}/       // DD-MM-YYYY
    ];

    let dateValue = null;
    for (const regex of dateRegexes) {
      const match = content.match(regex);
      if (match && match[0]) {
        dateValue = match[0];
        break;
      }
    }

    // Assign balance if found
    if (balance !== null) {
      model.balance = balance;
    }

    // If a date was found, convert it to a standardized Date object
    if (dateValue) {
      let dateObj = null;
      if (dateValue.includes('/')) {
        // Format: YYYY/MM/DD
        dateObj = new Date(dateValue.replace(/\//g, '-')); 
      } else if (dateValue.includes('-')) {
        // Format: DD-MM-YYYY
        const parts = dateValue.split('-');
        const reformatted = `${parts[2]}-${parts[1]}-${parts[0]}`; 
        dateObj = new Date(reformatted);
      }

      if (dateObj && !isNaN(dateObj.getTime())) {
        model.expired = dateObj;
      }
    }

    await model.save();
    console.log("SMS log saved successfully");
  } catch (err) {
    console.error("Error saving SMS log:", err);
  }
};

const handleGpsContent = async (deviceNumber, response, foundDevice) => {
  try {
    // Sanitize the response to handle potential encoding issues
    const sanitizedResponse = response
      .replace(/[\u0000-\u001F\u007F-\u009F\u00AD\u0600-\u0604\u070F\u17B4\u17B5\u200C-\u200F\u2028-\u202F\u2060-\u206F\uFEFF\uFFF0-\uFFFF]/g, '')
      .trim();
    
    // Try to parse the sanitized JSON
    let content;
    try {
      content = JSON.parse(sanitizedResponse);
    } catch (parseError) {
      console.log(`Invalid JSON for device ${deviceNumber}, saving raw payload instead`);
      // If parsing fails, save the raw payload instead
      content = { rawPayload: sanitizedResponse };
    }

    const payloadToSave = {
      deviceNumber,
      payload: JSON.stringify(content),
      deviceType: foundDevice.type,
    };

    await carGps.create(payloadToSave);
    // console.log(`GPS data saved for device ${deviceNumber}`);
  } catch (err) {
    console.error("Error logging GPS data:", err);
  }
};

// Function to publish scheduled commands with response handling
const schedpublishWithResponse = async (message, timeout = 10000) => {
  return new Promise((resolve, reject) => {
    try {
      if (!client || !client.connected) {
        console.error("MQTT client not connected");
        return resolve({ success: false, error: "mqtt-not-connected" });
      }

      const { topic, payload, qos = 0 } = message;
      
      // Ensure payload is a string
      const stringPayload = typeof payload === 'object' ? JSON.stringify(payload) : payload;
      
      console.log(`Publishing to ${topic} with response handling: ${stringPayload}`);
      
      // Register a response handler for this device
      registerCommandResponseHandler(topic, (response) => {
        console.log(`Received response for command to ${topic}:`, response);
        resolve({ success: true, response });
      });
      
      // Set a timeout to resolve with failure if no response is received
      const timeoutId = setTimeout(() => {
        if (commandResponseHandlers.has(topic)) {
          console.log(`Command to ${topic} timed out after ${timeout}ms`);
          commandResponseHandlers.delete(topic);
          resolve({ success: false, error: "timeout" });
        }
      }, timeout);
      
      // Publish the message
      client.publish(topic, stringPayload, { qos, retain: false }, (err) => {
        if (err) {
          console.error(`Error publishing to ${topic}:`, err);
          clearTimeout(timeoutId);
          commandResponseHandlers.delete(topic);
          return resolve({ success: false, error: err.message });
        }
        
        console.log(`Successfully published to ${topic}`);
        // Now waiting for response or timeout
      });
    } catch (error) {
      console.error("Error in schedpublishWithResponse:", error);
      return resolve({ success: false, error: error.message });
    }
  });
};

// Make sure to call initializeMqttClient when the module is loaded
initializeMqttClient();

// Export the client instance and functions
module.exports = {
  initializeMqttClient,
  subscribe,
  unsubscribe,
  subscribeCar2,
  unsubscribeCar2,
  subscribeCommands,
  publish,
  schedpublish,
  schedpublishWithResponse,
  registerCommandResponseHandler,
  removeCommandResponseHandler,
  client // Export the client instance
};
