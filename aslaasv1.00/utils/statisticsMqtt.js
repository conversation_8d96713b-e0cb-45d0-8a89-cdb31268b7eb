const mqtt = require('mqtt');
const LogModel = require('../models/log');
const UserModel = require('../models/user');
const DeviceModel = require('../models/device');

class StatisticsMqttPublisher {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    
    // Statistics topics
    this.topics = {
      commands: 'statistics/commands',
      devices: 'statistics/devices',
      users: 'statistics/users',
      realtime: 'statistics/realtime',
      filters: 'statistics/filters',
      refresh: 'statistics/refresh'
    };
    
    // Cache for real-time statistics
    this.realtimeCache = {
      newCommands: 0,
      activeDevices: 0,
      activeUsers: 0,
      lastReset: Date.now()
    };
    
    // Initialize connection
    this.connect();
    
    // Set up periodic real-time updates
    this.setupPeriodicUpdates();
  }

  // Connect to MQTT broker
  connect() {
    try {
      // Use the same MQTT broker as the main system
      const brokerUrl = process.env.MQTT_BROKER_URL || 'mqtt://39.104.209.84:1883';
      
      this.client = mqtt.connect(brokerUrl, {
        clientId: `statistics_publisher_${Math.random().toString(16).substring(2, 10)}`,
        clean: true,
        keepalive: 60,
        reconnectPeriod: this.reconnectDelay
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error('Statistics MQTT: Connection error', error);
      this.scheduleReconnect();
    }
  }

  // Setup MQTT event handlers
  setupEventHandlers() {
    if (!this.client) return;

    this.client.on('connect', () => {
      console.log('Statistics MQTT: Publisher connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Subscribe to filter and refresh requests
      this.client.subscribe([this.topics.filters, this.topics.refresh]);
    });

    this.client.on('disconnect', () => {
      console.log('Statistics MQTT: Publisher disconnected');
      this.isConnected = false;
    });

    this.client.on('error', (error) => {
      console.error('Statistics MQTT: Publisher error', error);
      this.isConnected = false;
      this.scheduleReconnect();
    });

    this.client.on('message', (topic, message) => {
      try {
        const data = JSON.parse(message.toString());
        
        if (topic === this.topics.filters) {
          console.log('Statistics MQTT: Received filter update', data);
          // Handle filter updates from clients
        } else if (topic === this.topics.refresh) {
          console.log('Statistics MQTT: Received refresh request', data);
          // Handle refresh requests from clients
          this.handleRefreshRequest(data);
        }
      } catch (error) {
        console.error('Statistics MQTT: Error processing message', error);
      }
    });
  }

  // Schedule reconnection
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Statistics MQTT: Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Statistics MQTT: Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  // Publish command statistics update
  publishCommandUpdate(commandData) {
    if (!this.isConnected) return;

    const message = {
      type: 'command_update',
      data: commandData,
      timestamp: Date.now()
    };

    this.client.publish(this.topics.commands, JSON.stringify(message));
    
    // Update real-time cache
    this.realtimeCache.newCommands++;
  }

  // Publish device statistics update
  publishDeviceUpdate(deviceData) {
    if (!this.isConnected) return;

    const message = {
      type: 'device_update',
      data: deviceData,
      timestamp: Date.now()
    };

    this.client.publish(this.topics.devices, JSON.stringify(message));
  }

  // Publish user activity update
  publishUserUpdate(userData) {
    if (!this.isConnected) return;

    const message = {
      type: 'user_update',
      data: userData,
      timestamp: Date.now()
    };

    this.client.publish(this.topics.users, JSON.stringify(message));
  }

  // Publish real-time statistics
  publishRealtimeStats() {
    if (!this.isConnected) return;

    const message = {
      type: 'realtime_stats',
      data: { ...this.realtimeCache },
      timestamp: Date.now()
    };

    this.client.publish(this.topics.realtime, JSON.stringify(message));
  }

  // Handle refresh requests from clients
  async handleRefreshRequest(requestData) {
    try {
      const { filters = {} } = requestData;
      
      // Get current statistics based on filters
      const stats = await this.getCurrentStatistics(filters);
      
      // Publish updated statistics
      this.publishRealtimeStats();
      
      if (stats.commands) {
        this.publishCommandUpdate(stats.commands);
      }
      
      if (stats.devices) {
        this.publishDeviceUpdate(stats.devices);
      }
      
      if (stats.users) {
        this.publishUserUpdate(stats.users);
      }
    } catch (error) {
      console.error('Statistics MQTT: Error handling refresh request', error);
    }
  }

  // Get current statistics from database
  async getCurrentStatistics(filters = {}) {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      // Build match conditions
      let matchConditions = {
        createdAt: { $gte: oneHourAgo }
      };
      
      if (filters.deviceNumber) {
        matchConditions.deviceNumber = filters.deviceNumber;
      }
      
      if (filters.userId) {
        matchConditions.userId = filters.userId;
      }

      // Get active devices count
      const activeDevices = await LogModel.distinct('deviceNumber', matchConditions);
      
      // Get active users count
      const activeUsers = await LogModel.distinct('userId', matchConditions);
      
      // Update real-time cache
      this.realtimeCache.activeDevices = activeDevices.length;
      this.realtimeCache.activeUsers = activeUsers.length;
      
      return {
        commands: { recentCount: await LogModel.countDocuments(matchConditions) },
        devices: { activeCount: activeDevices.length },
        users: { activeCount: activeUsers.length }
      };
    } catch (error) {
      console.error('Statistics MQTT: Error getting current statistics', error);
      return {};
    }
  }

  // Setup periodic updates
  setupPeriodicUpdates() {
    // Publish real-time stats every 30 seconds
    setInterval(() => {
      this.publishRealtimeStats();
    }, 30000);

    // Reset counters every hour
    setInterval(() => {
      this.realtimeCache.newCommands = 0;
      this.realtimeCache.lastReset = Date.now();
    }, 3600000);

    // Update active counts every 5 minutes
    setInterval(async () => {
      try {
        await this.getCurrentStatistics();
      } catch (error) {
        console.error('Statistics MQTT: Error in periodic update', error);
      }
    }, 300000);
  }

  // Disconnect from MQTT
  disconnect() {
    if (this.client) {
      this.client.end();
      this.client = null;
    }
    this.isConnected = false;
  }
}

// Create singleton instance
const statisticsMqttPublisher = new StatisticsMqttPublisher();

module.exports = statisticsMqttPublisher;
