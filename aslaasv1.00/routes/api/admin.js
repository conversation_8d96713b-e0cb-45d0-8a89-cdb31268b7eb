const express = require('express');
const router = express.Router();
const controller = require('../../controller/adminController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');
const adminOrInstaller = require('../../middleware/adminOrInstaller');

/****************** */

router.get('/user/list', auth, adminOrInstaller, controller.userList);
router.post('/user/wallet-change', auth, admin, controller.walletChange);
router.get('/wallet-requests', auth, admin, controller.walletRequest);
router.get('/rent-car-status', auth, admin, controller.rentCarStatus);
router.get('/wallet-transactions', auth, admin, controller.walletTransactions);
router.post('/user/delete', auth, admin, controller.removeUsers);
router.post('/user/change-active', auth, admin, controller.changeActive);
router.post('/user/driver-license-verification', auth, admin, controller.driverLicenseVerification);
router.post('/user/extend-license', auth, admin, controller.extendsLicense);
router.get('/order/list', auth, admin, controller.orderList);
router.post('/notification/send',auth,admin,controller.sendNotifications);

// Routes accessible by both admin and installer roles
router.post('/user/reset-pin', auth, adminOrInstaller, controller.resetUserPin);

// Admin-only routes
router.post('/user/set-role', auth, admin, controller.setUserRole);

// Statistics routes - Admin only
router.get('/statistics/commands', auth, admin, controller.getCommandStatistics);
router.get('/statistics/top-users', auth, admin, controller.getTopUsers);
router.get('/statistics/device-analytics', auth, admin, controller.getDeviceAnalytics);
router.get('/statistics/time-based', auth, admin, controller.getTimeBasedStatistics);
router.get('/statistics/detailed-logs', auth, admin, controller.getDetailedCommandLogs);

// Development helper - Create sample data
router.post('/statistics/create-sample-data', auth, admin, controller.createSampleData);

module.exports = router;