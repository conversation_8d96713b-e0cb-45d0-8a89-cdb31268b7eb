#!/bin/bash

# Script to fix @mui/lab imports by replacing them with comments
# and adding CircularProgress to MUI imports where needed

echo "Fixing @mui/lab imports..."

# List of files that need to be fixed
files=(
    "src/sections/auth/ForgotPasswordForm.js"
    "src/sections/auth/VerifyCodeForm.js"
    "src/sections/auth/LoginForm.js"
    "src/sections/admin/user/SendNotificationDilaog.js"
    "src/pages/DriverProfile.js"
    "src/pages/profile.js"
    "src/pages/Notification.js"
    "src/pages/DeviceConfig.js"
    "src/pages/admin/DeviceDialog.js"
    "src/pages/admin/DeviceEdit.js"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "Processing $file..."
        
        # Replace LoadingButton import with comment
        sed -i.bak "s/import { LoadingButton } from '@mui\/lab';/\/\/ LoadingButton replaced with regular Button + CircularProgress/g" "$file"
        sed -i.bak "s/import { LoadingButton } from \"@mui\/lab\";/\/\/ LoadingButton replaced with regular Button + CircularProgress/g" "$file"
        
        # Replace other @mui/lab imports with comments
        sed -i.bak "s/import.*from '@mui\/lab';/\/\/ MUI Lab components replaced with regular components/g" "$file"
        sed -i.bak "s/import.*from \"@mui\/lab\";/\/\/ MUI Lab components replaced with regular components/g" "$file"
        
        # Add CircularProgress to MUI imports if not already present
        if grep -q "from '@mui/material'" "$file" && ! grep -q "CircularProgress" "$file"; then
            sed -i.bak "s/} from '@mui\/material';/, CircularProgress, Button } from '@mui\/material';/g" "$file"
        fi
        
        # Remove backup files
        rm -f "$file.bak"
        
        echo "Fixed $file"
    else
        echo "File $file not found, skipping..."
    fi
done

echo "Done fixing @mui/lab imports!"
