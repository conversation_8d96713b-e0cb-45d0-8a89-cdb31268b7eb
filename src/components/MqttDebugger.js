import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  Chip,
  Button,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Wifi as WifiIcon,
  WifiOff as WifiOffIcon,
  BugReport as BugIcon
} from '@mui/icons-material';
import mqttService from '../services/mqttService';
import useAuth from '../hooks/useAuth';

const MqttDebugger = () => {
  const { user } = useAuth();
  const [connectionStatus, setConnectionStatus] = useState({
    isConnected: false,
    isConnecting: false,
    lastError: null,
    connectionAttempts: 0,
    lastConnectedAt: null,
    lastDisconnectedAt: null
  });
  const [logs, setLogs] = useState([]);
  const [expanded, setExpanded] = useState(false);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-19), { timestamp, message, type }]);
  };

  useEffect(() => {
    // Monitor MQTT connection status
    const updateStatus = () => {
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: mqttService.isConnected
      }));
    };

    // Set up MQTT event listeners
    const handleConnect = () => {
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: true,
        isConnecting: false,
        lastConnectedAt: new Date(),
        lastError: null
      }));
      addLog('MQTT Connected successfully', 'success');
    };

    const handleDisconnect = () => {
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
        lastDisconnectedAt: new Date()
      }));
      addLog('MQTT Disconnected', 'warning');
    };

    const handleError = (error) => {
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
        lastError: error?.message || 'Unknown error'
      }));
      addLog(`MQTT Error: ${error?.message || 'Unknown error'}`, 'error');
    };

    const handleReconnect = () => {
      setConnectionStatus(prev => ({
        ...prev,
        isConnecting: true,
        connectionAttempts: prev.connectionAttempts + 1
      }));
      addLog('MQTT Reconnecting...', 'info');
    };

    // Add event listeners
    mqttService.on('connect', handleConnect);
    mqttService.on('disconnect', handleDisconnect);
    mqttService.on('error', handleError);
    mqttService.on('reconnect', handleReconnect);

    // Listen for custom browser events
    const handleMqttConnected = () => {
      addLog('Browser MQTT event: Connected', 'success');
    };

    const handleMqttDisconnected = () => {
      addLog('Browser MQTT event: Disconnected', 'warning');
    };

    const handleMqttFailed = (event) => {
      addLog(`Browser MQTT event: Connection failed - ${event.detail?.message || 'Unknown'}`, 'error');
    };

    window.addEventListener('mqtt-connected', handleMqttConnected);
    window.addEventListener('mqtt-disconnected', handleMqttDisconnected);
    window.addEventListener('mqtt-connection-failed', handleMqttFailed);

    // Initial status check
    updateStatus();
    addLog('MQTT Debugger initialized', 'info');

    return () => {
      // Cleanup event listeners
      mqttService.off('connect', handleConnect);
      mqttService.off('disconnect', handleDisconnect);
      mqttService.off('error', handleError);
      mqttService.off('reconnect', handleReconnect);
      
      window.removeEventListener('mqtt-connected', handleMqttConnected);
      window.removeEventListener('mqtt-disconnected', handleMqttDisconnected);
      window.removeEventListener('mqtt-connection-failed', handleMqttFailed);
    };
  }, []);

  const handleManualConnect = () => {
    if (!user?.device?.deviceNumber) {
      addLog('Error: No device number found', 'error');
      return;
    }

    const brokerAddress = user?.device?.renter || "elec.mn";
    const brokerUrl = `wss://${brokerAddress}:8084/mqtt`;
    
    addLog(`Attempting manual connection to ${brokerUrl}`, 'info');
    setConnectionStatus(prev => ({ ...prev, isConnecting: true }));

    const success = mqttService.connect(brokerUrl, {
      clean: true,
      keepalive: 60,
      reconnectPeriod: 1000,
      connectTimeout: 30000,
      clientId: `debug_${user.device.deviceNumber}_${Math.random().toString(16).substring(2, 10)}`,
    });

    if (!success) {
      addLog('Failed to initiate connection', 'error');
      setConnectionStatus(prev => ({ ...prev, isConnecting: false }));
    }
  };

  const handleDisconnect = () => {
    mqttService.disconnect();
    addLog('Manual disconnect requested', 'info');
  };

  const getStatusColor = () => {
    if (connectionStatus.isConnecting) return 'warning';
    if (connectionStatus.isConnected) return 'success';
    return 'error';
  };

  const getStatusText = () => {
    if (connectionStatus.isConnecting) return 'Connecting...';
    if (connectionStatus.isConnected) return 'Connected';
    return 'Disconnected';
  };

  const getStatusIcon = () => {
    if (connectionStatus.isConnected) return <WifiIcon />;
    return <WifiOffIcon />;
  };

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <BugIcon color="primary" />
            <Typography variant="h6">MQTT Connection Debugger</Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={1}>
            <Chip
              icon={getStatusIcon()}
              label={getStatusText()}
              color={getStatusColor()}
              variant="outlined"
            />
            <Tooltip title="Refresh status">
              <IconButton size="small" onClick={() => window.location.reload()}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Connection Info */}
        <Box mb={2}>
          <Typography variant="body2" color="text.secondary">
            Device: {user?.device?.deviceNumber || 'Not set'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Broker: {user?.device?.renter || 'elec.mn'}:8084
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Protocol: {user?.device?.type || 'Unknown'}
          </Typography>
        </Box>

        {/* Error Display */}
        {connectionStatus.lastError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Last Error: {connectionStatus.lastError}
          </Alert>
        )}

        {/* Connection Controls */}
        <Box display="flex" gap={1} mb={2}>
          <Button
            variant="contained"
            onClick={handleManualConnect}
            disabled={connectionStatus.isConnecting || connectionStatus.isConnected}
            size="small"
          >
            Connect
          </Button>
          <Button
            variant="outlined"
            onClick={handleDisconnect}
            disabled={!connectionStatus.isConnected}
            size="small"
          >
            Disconnect
          </Button>
        </Box>

        {/* Detailed Logs */}
        <Accordion expanded={expanded} onChange={() => setExpanded(!expanded)}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">
              Connection Logs ({logs.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List dense>
              {logs.slice(-10).map((log, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemText
                    primary={log.message}
                    secondary={log.timestamp}
                    primaryTypographyProps={{
                      variant: 'body2',
                      color: log.type === 'error' ? 'error' : 
                             log.type === 'warning' ? 'warning.main' :
                             log.type === 'success' ? 'success.main' : 'text.primary'
                    }}
                  />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Connection Stats */}
        <Box mt={2} display="flex" gap={2} flexWrap="wrap">
          <Typography variant="caption" color="text.secondary">
            Attempts: {connectionStatus.connectionAttempts}
          </Typography>
          {connectionStatus.lastConnectedAt && (
            <Typography variant="caption" color="text.secondary">
              Last Connected: {connectionStatus.lastConnectedAt.toLocaleTimeString()}
            </Typography>
          )}
          {connectionStatus.lastDisconnectedAt && (
            <Typography variant="caption" color="text.secondary">
              Last Disconnected: {connectionStatus.lastDisconnectedAt.toLocaleTimeString()}
            </Typography>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default MqttDebugger;
