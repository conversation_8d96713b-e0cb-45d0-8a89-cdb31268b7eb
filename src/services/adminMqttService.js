// Copy entire file content

class AdminMqttService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000; // 5 seconds
    this.clientId = `admin_${Date.now()}`;
    this.subscribers = new Map(); // Store callback functions for different topics
    this.statisticsCallbacks = new Set(); // Callbacks for statistics updates
    
    // MQTT Configuration - Use same config as existing system
    this.primaryBroker = 'elec.mn';
    this.fallbackBroker = 'broker.emqx.io';
    this.port = 8084;
    this.protocol = 'wss'; // Use secure WebSocket

    this.mqttConfig = {
      clean: true,
      keepalive: 60,
      reconnectPeriod: this.reconnectInterval,
      connectTimeout: 15000, // Shorter timeout for faster fallback
      clientId: this.clientId,
    };

    console.log(`Admin MQTT Service initialized with client ID: ${this.clientId}`);
  }

  // Connect to MQTT broker
  connect() {
    if (this.client && this.isConnected) {
      console.log('Admin MQTT: Already connected');
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      try {
        // Try primary broker first
        const primaryUrl = `${this.protocol}://${this.primaryBroker}:${this.port}/mqtt`;
        const fallbackUrl = `${this.protocol}://${this.fallbackBroker}:${this.port}/mqtt`;

        console.log(`Admin MQTT: Connecting to primary broker: ${primaryUrl}`);
        console.log(`Admin MQTT: Fallback broker: ${fallbackUrl}`);

        this.client = mqtt.connect(primaryUrl, this.mqttConfig);

        this.client.on('connect', () => {
          console.log('Admin MQTT: Connected successfully');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Subscribe to statistics topics
          this.subscribeToStatisticsTopics();
          
          resolve();
        });

        this.client.on('error', (error) => {
          console.error('Admin MQTT: Primary broker connection error:', error);
          this.isConnected = false;

          // Try fallback broker
          console.log('Admin MQTT: Trying fallback broker...');
          this.client = mqtt.connect(fallbackUrl, this.mqttConfig);

          this.client.on('connect', () => {
            console.log('Admin MQTT: Connected to fallback broker successfully');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.subscribeToStatisticsTopics();
            resolve();
          });

          this.client.on('error', (fallbackError) => {
            console.error('Admin MQTT: Fallback broker also failed:', fallbackError);
            reject(fallbackError);
          });
        });

        this.client.on('close', () => {
          console.log('Admin MQTT: Connection closed');
          this.isConnected = false;
          this.handleReconnect();
        });

        this.client.on('offline', () => {
          console.log('Admin MQTT: Client offline');
          this.isConnected = false;
        });

        this.client.on('message', (topic, message) => {
          this.handleMessage(topic, message);
        });

      } catch (error) {
        console.error('Admin MQTT: Failed to create connection:', error);
        reject(error);
      }
    });
  }

  // Subscribe to statistics-related topics
  subscribeToStatisticsTopics() {
    if (!this.client || !this.isConnected) {
      console.warn('Admin MQTT: Cannot subscribe - not connected');
      return;
    }

    const topics = [
      'statistics/commands',      // Real-time command statistics
      'statistics/responses',     // Real-time response updates
      'statistics/devices',       // Device status updates
      'statistics/users',         // User activity updates
      '+/msg',                   // All device responses (for real-time monitoring)
    ];

    topics.forEach(topic => {
      this.client.subscribe(topic, { qos: 1 }, (err) => {
        if (err) {
          console.error(`Admin MQTT: Failed to subscribe to ${topic}:`, err);
        } else {
          console.log(`Admin MQTT: Subscribed to ${topic}`);
        }
      });
    });
  }

  // Handle incoming MQTT messages
  handleMessage(topic, message) {
    try {
      const messageStr = message.toString();
      console.log(`Admin MQTT: Received message on ${topic}:`, messageStr);

      // Handle device responses (real-time command monitoring)
      if (topic.endsWith('/msg')) {
        const deviceNumber = topic.replace('/msg', '');
        this.handleDeviceResponse(deviceNumber, messageStr);
      }

      // Handle statistics updates
      if (topic.startsWith('statistics/')) {
        this.handleStatisticsUpdate(topic, messageStr);
      }

      // Notify subscribers
      if (this.subscribers.has(topic)) {
        const callbacks = this.subscribers.get(topic);
        callbacks.forEach(callback => {
          try {
            callback(topic, messageStr);
          } catch (error) {
            console.error('Admin MQTT: Error in subscriber callback:', error);
          }
        });
      }

    } catch (error) {
      console.error('Admin MQTT: Error handling message:', error);
    }
  }

  // Handle device responses for real-time monitoring
  handleDeviceResponse(deviceNumber, response) {
    const responseData = {
      deviceNumber,
      response,
      timestamp: new Date(),
      type: 'device_response'
    };

    // Notify statistics callbacks
    this.statisticsCallbacks.forEach(callback => {
      try {
        callback(responseData);
      } catch (error) {
        console.error('Admin MQTT: Error in statistics callback:', error);
      }
    });
  }

  // Handle statistics updates
  handleStatisticsUpdate(topic, message) {
    try {
      const data = JSON.parse(message);
      const updateData = {
        topic,
        data,
        timestamp: new Date(),
        type: 'statistics_update'
      };

      // Notify statistics callbacks
      this.statisticsCallbacks.forEach(callback => {
        try {
          callback(updateData);
        } catch (error) {
          console.error('Admin MQTT: Error in statistics update callback:', error);
        }
      });

    } catch (error) {
      console.log('Admin MQTT: Non-JSON statistics message:', message);
    }
  }

  // Subscribe to a specific topic
  subscribe(topic, callback) {
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, new Set());
    }
    this.subscribers.get(topic).add(callback);

    if (this.client && this.isConnected) {
      this.client.subscribe(topic, { qos: 1 }, (err) => {
        if (err) {
          console.error(`Admin MQTT: Failed to subscribe to ${topic}:`, err);
        } else {
          console.log(`Admin MQTT: Subscribed to ${topic}`);
        }
      });
    }
  }

  // Unsubscribe from a topic
  unsubscribe(topic, callback) {
    if (this.subscribers.has(topic)) {
      this.subscribers.get(topic).delete(callback);
      
      if (this.subscribers.get(topic).size === 0) {
        this.subscribers.delete(topic);
        
        if (this.client && this.isConnected) {
          this.client.unsubscribe(topic, (err) => {
            if (err) {
              console.error(`Admin MQTT: Failed to unsubscribe from ${topic}:`, err);
            } else {
              console.log(`Admin MQTT: Unsubscribed from ${topic}`);
            }
          });
        }
      }
    }
  }

  // Add callback for statistics updates
  onStatisticsUpdate(callback) {
    this.statisticsCallbacks.add(callback);
    return () => this.statisticsCallbacks.delete(callback);
  }

  // Publish statistics update (for triggering refreshes)
  publishStatisticsRefresh() {
    if (this.client && this.isConnected) {
      const message = JSON.stringify({
        type: 'refresh_request',
        timestamp: new Date(),
        clientId: this.clientId
      });
      
      this.client.publish('statistics/refresh', message, { qos: 1 }, (err) => {
        if (err) {
          console.error('Admin MQTT: Failed to publish statistics refresh:', err);
        } else {
          console.log('Admin MQTT: Published statistics refresh request');
        }
      });
    }
  }

  // Handle reconnection
  handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Admin MQTT: Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.log(`Admin MQTT: Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      if (!this.isConnected) {
        this.connect().catch(error => {
          console.error('Admin MQTT: Reconnection failed:', error);
        });
      }
    }, this.reconnectInterval * this.reconnectAttempts);
  }

  // Disconnect from MQTT
  disconnect() {
    if (this.client) {
      console.log('Admin MQTT: Disconnecting...');
      this.client.end();
      this.client = null;
      this.isConnected = false;
      this.subscribers.clear();
      this.statisticsCallbacks.clear();
    }
  }

  // Get connection status
  getStatus() {
    return {
      isConnected: this.isConnected,
      clientId: this.clientId,
      reconnectAttempts: this.reconnectAttempts,
      subscriberCount: this.subscribers.size,
      statisticsCallbackCount: this.statisticsCallbacks.size
    };
  }
}

// Create singleton instance
const adminMqttService = new AdminMqttService();

export default adminMqttService;
