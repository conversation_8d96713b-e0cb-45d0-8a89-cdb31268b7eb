import { useState, useEffect } from "react";
import { useSnackbar } from "notistack";
import { motion } from "framer-motion";

// @mui
import SatelliteAltIcon from "@mui/icons-material/SatelliteAlt";

import {
  Card,
  Container,
  Grid,
  Box,
  Stack,
  Typography,
  Divider,
  Alert,
} from "@mui/material";
import "leaflet/dist/leaflet.css";
import { useTranslation } from "react-i18next";
import SeasonalAnimation from "../components/SeasonalAnimation";
// pusher
import useSound from "use-sound";

import io from "socket.io-client";

import engine from "../assets/engine.mp3";
import lock from "../assets/lock.mp3";
// components
import Page from "../components/Page";
import Car from "../components/Car";

import ChipIcon from "../components/ChipIconi";

import Iconify from "../components/Iconify";
// import MqttDebugger from "../components/MqttDebugger";

import FindingScreen from "../components/FindingScreen";
// hooks
import useResponsive from "../hooks/useResponsive";
import useAuth from "../hooks/useAuth";
import useLocalStorage from "../hooks/useLocalStorage";
import Layout from "../layout";
import axios from "../utils/axios";
import CarLocation from "./CarLocation";
import {  HOST_API } from "../config";
import PinCodeConfirm from "../layout/PinCodeConfirm";

import { fShortenNumber } from "../utils/formatUtils";
import DeviceController from "../components/DeviceController";
import CarSide from "../components/CarSide";
import { useNavigate } from "react-router";
import WeatherDisplay from "../layout/weather";
import { InfoOutlined } from "@mui/icons-material";
import PinCodeSetupDialog from "../components/PinCodeSetupDialog";
import ScrollablePowerButton from "../components/ScrollablePowerButton";
import ScrollableMapButton from "../components/ScrollableMapButton";
import ScrollableLockButton from "../components/ScrollableLockButton";
import SchedulerDialog from "../components/SchedulerDialog";
import ScheduleButton from "../components/ScheduleButton";
import GpsHistoryButton from "../components/GpsHistoryButton";
import MirrorControlButton from "../components/MirrorControlButton";
// mqtt
import mqttService from "../services/mqttService";
// Helper function to format version string
const formatVersion = (version) => {
  if (!version) return "N/A";
  
  // If version is shorter than 4 characters, return as is
  if (version.length <= 4) return version;
  
  // Otherwise return first 4 characters
  return version.substring(0, 4);
};
export default function Home() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("initializing...");
  const [description, setDescription] = useState("");
  const { initialize, user } = useAuth();
  const [showPincodeSetup, setShowPincodeSetup] = useState(false);
  const status = null;
  const [action, setAction] = useState("");
  const [deviceStatus, setDeviceStatus] = useState();
  const [color, setColor] = useState("gray");
  const [viewMap, setViewMap] = useState(false);

  const [ setMotionTime] = useState();
  const { enqueueSnackbar } = useSnackbar();

  const [deviceNumber] = useState(user?.device?.deviceNumber);
  const [checkOnline, setCheckOnline] = useState(false);
  const [command, setCommand] = useState("check");
  const [checkedPincode, setCheckedPincode] = useState(true);
  const [iconChange, setChangeicon] = useState(false);
  const [online, setOnline] = useState(false);
  const { t } = useTranslation();
  const [play] = useSound(engine);
  const navigate = useNavigate();
  const [simStatus, setSimStatus] = useState({ balance: '', expiredDate: '' });

  // Protocol and MQTT states
  const [protocol] = useLocalStorage(`device_${user?.device?.deviceNumber}_protocol`, 'tcp');
  const [mqttConnected, setMqttConnected] = useState(false);
  const [mqttConnecting, setMqttConnecting] = useState(false);

  // Toggle button states
  const [isLocked, setIsLocked] = useState(true); // Assume locked by default
  const [isEngineOn, setIsEngineOn] = useState(false); // Assume engine off by default

  // Engine button hold animation states
  const [isEngineHolding, setIsEngineHolding] = useState(false);
  const [engineHoldProgress, setEngineHoldProgress] = useState(0);
  const [engineHoldTimer, setEngineHoldTimer] = useState(null);

  // Button position for bird animation
  const [buttonPosition, setButtonPosition] = useState(null);

  // Nikola Semi design system with CSS variables for theme switching
  const nikolaColors = {
    // Base neutral colors
    base: {
      dark: '#0f0f0f',      // Deep black
      gray900: '#1a1a1a',   // Dark gray
      gray800: '#262626',   // Medium dark gray
      gray700: '#404040',   // Medium gray
      gray600: '#525252',   // Light gray
      gray500: '#737373',   // Neutral gray
      gray400: '#a3a3a3',   // Light neutral
      gray300: '#d4d4d4',   // Very light gray
      gray200: '#e5e5e5',   // Off white
      white: '#ffffff'      // Pure white
    },
    // Accent colors - consistent across themes
    accent: {
      teal: '#14b8a6',      // Primary accent
      cyan: '#06b6d4',      // Secondary accent
      green: '#10b981',     // Success/unlock state
      amber: '#f59e0b',     // Warning/caution
      red: '#ef4444',       // Alert/danger
      orange: '#f97316'     // Navigation/location
    },
    // Theme variables - Premium automotive dark mode
    theme: {
      dark: {
        bgColor: '#000000',      // Pure black for premium feel
        bgGradient: 'linear-gradient(180deg, #000000 0%, #111111 100%)',
        textColor: '#f0f0f0',    // Clean off-white
        buttonBg: '#262626',     // Dark gray buttons
        containerBg: 'transparent' // Transparent containers
      },
      light: {
        bgColor: '#ffffff',      // Pure white
        bgGradient: 'linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%)',
        textColor: '#333333',    // Dark gray text
        buttonBg: '#f5f5f5',     // Light gray buttons
        containerBg: 'rgba(255, 255, 255, 0.95)' // Semi-transparent white
      }
    }
  };

  // Nikola Semi button color system - clean uniform styling
  const buttonColors = {
    // Lock/Unlock: Balanced visual hierarchy with alert states
    lock: {
      locked: {
        accent: nikolaColors.base.gray500, // Neutral when locked (secure state)
        text: '#f0f0f0',
        icon: '#737373' // Gray for neutral/secure state
      },
      unlocked: {
        accent: nikolaColors.accent.red, // Red alert when unlocked (warning state)
        text: '#f0f0f0',
        icon: nikolaColors.accent.red // Red indicates alert/warning
      },
      locking: {
        accent: nikolaColors.accent.green, // Green when actively locking (action confirmation)
        text: '#f0f0f0',
        icon: nikolaColors.accent.green // Green confirms positive action
      }
    },
    // Engine: Clean neutral with teal/amber accents
    engine: {
      off: {
        accent: nikolaColors.accent.teal,
        text: '#f0f0f0',
        icon: nikolaColors.accent.teal
      },
      on: {
        accent: nikolaColors.accent.amber,
        text: '#f0f0f0',
        icon: nikolaColors.accent.amber
      }
    },
    // Location: Clean neutral with orange accent
    location: {
      accent: nikolaColors.accent.orange,
      text: '#f0f0f0',
      icon: nikolaColors.accent.orange
    },
    // Schedule: Clean neutral with cyan accent
    schedule: {
      accent: nikolaColors.accent.cyan,
      text: '#f0f0f0',
      icon: nikolaColors.accent.cyan
    },
    // History: Clean neutral with teal accent
    history: {
      accent: nikolaColors.accent.teal,
      text: '#f0f0f0',
      icon: nikolaColors.accent.teal
    },
    // Mirror: Pure neutral (hardware control)
    mirror: {
      accent: nikolaColors.base.gray500,
      text: '#f0f0f0',
      icon: '#d4d4d4'
    },
    // Power button colors (for ScrollablePowerButton)
    power: {
      off: {
        accent: nikolaColors.accent.teal,
        text: '#f0f0f0',
        icon: nikolaColors.accent.teal
      },
      on: {
        accent: nikolaColors.accent.amber,
        text: '#f0f0f0',
        icon: nikolaColors.accent.amber
      }
    }
  };

  // Nikola Semi button style generator - clean minimal design
  const createButtonStyle = (colorScheme, size = 90) => ({
    width: size,
    height: size,
    borderRadius: '16px',
    background: nikolaColors.theme.dark.buttonBg, // Uniform dark gray background
    border: 'none', // Remove all borders
    // Clean subtle shadow - no colored outlines
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)',
    transition: "all 0.15s cubic-bezier(0.4, 0, 0.2, 1)",
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    position: 'relative',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTouchCallout: 'none',
    WebkitTapHighlightColor: 'transparent',
    // Enhanced micro-interactions with elevation and glow
    '&:hover': {
      transform: 'translateY(-1px)', // Subtle elevation
      boxShadow: `
        0 4px 8px rgba(0, 0, 0, 0.2),
        inset 0 0 5px ${colorScheme.accent || nikolaColors.accent.teal}30
      `, // Increased shadow + inner glow
    },
    '&:active': {
      transform: 'translateY(1px)', // Depression effect
      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.15)', // Reduced shadow on press
      transition: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)', // Smooth color transition
    },
    // Disabled state
    '&:disabled': {
      opacity: 0.4,
      filter: 'grayscale(100%)',
      cursor: 'not-allowed',
      transform: 'none',
    }
  });

  // Button bouncing animation states
  const [bouncingButton, setBouncingButton] = useState(null); // 'power', 'lock', 'location', or null

  // Scheduler dialog state
  const [schedulerDialogOpen, setSchedulerDialogOpen] = useState(false);

  const fetchSimStatus = async () => {
    if (deviceNumber) {
      try {
        const url = `/api/log/sim-status?deviceNumber=${deviceNumber}`;
        const response = await axios.get(url);
        if (response.data.success) {
          const fetchedData = response.data.data;
          const dateParts = fetchedData.expiredDate.split('/'); // Split by '/'
          const formattedExpiredDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`; // Convert to YYYY-MM-DD
          setSimStatus({
            balance: fetchedData.balance,
            expiredDate: formattedExpiredDate
          });
          // console.log("Fetched SIM status:", fetchedData); // Log fetched SIM status
        }
      } catch (error) {
        // console.error('Error fetching SIM status:', error);
      }
    } else {
      // console.log("deviceNumber is not set");
    }
  };

  const checkSimcard = async () => {
    try {
      const headers = {
        'Content-Type': 'application/json; charset=UTF-8',
      };

      const response = await axios.post(`/api/device/check-sim`, {
        deviceNumber: user.device?.deviceNumber
      }, { headers });

      if (response.status === 200) {
        // console.log("checksim:", response.data);
      }
    } catch {
      // console.error('Error:', error);
    }
  };

  // MQTT connection function
  const connectToMqttBroker = async () => {
    if (protocol !== 'tcp' || !user?.device?.deviceNumber) {
      return;
    }

    try {
      // Get broker address from user device or use fallback
      const brokerAddress = user?.device?.renter || "elec.mn";
      // Fallback to test broker if main broker is down
      const testBroker = "broker.emqx.io"; // Public test broker
      const brokerUrl = `wss://${brokerAddress}:8084/mqtt`;
      const fallbackUrl = `wss://${testBroker}:8084/mqtt`;

      console.log(`Primary broker: ${brokerUrl}`);
      console.log(`Fallback broker: ${fallbackUrl}`);

      // console.log(`MQTT: Connecting to broker for LwM2M protocol: ${brokerUrl}`);
      setMqttConnecting(true);
      setMqttConnected(false); // Ensure we start with disconnected state

      // Try primary broker first, then fallback
      let connectionStarted = mqttService.connect(brokerUrl, {
        clean: true,
        keepalive: 60,
        reconnectPeriod: 5000, // Retry every 5 seconds
        connectTimeout: 15000, // Shorter timeout for faster fallback
        clientId: `home_${user.device.deviceNumber}_${Math.random().toString(16).substring(2, 10)}`,
      });

      // If primary connection fails, try fallback after timeout
      if (connectionStarted) {
        setTimeout(() => {
          if (!mqttConnected && !mqttService.isConnected) {
            console.log('Primary broker failed, trying fallback...');
            enqueueSnackbar('Primary broker unavailable, trying backup...', { variant: 'warning' });

            mqttService.connect(fallbackUrl, {
              clean: true,
              keepalive: 60,
              reconnectPeriod: 5000,
              connectTimeout: 15000,
              clientId: `home_fallback_${user.device.deviceNumber}_${Math.random().toString(16).substring(2, 10)}`,
            });
          }
        }, 20000); // Wait 20 seconds before trying fallback
      }

      if (connectionStarted) {
        // Set up MQTT event listeners
        mqttService.on('connect', () => {
          // console.log('MQTT: Connected successfully for LwM2M protocol');
          setMqttConnected(true);
          setMqttConnecting(false);

          // Subscribe to device message topic (correct format: deviceNumber/msg)
          const responseTopic = `${user.device.deviceNumber}/msg`;
          mqttService.subscribe(responseTopic);
          // console.log(`MQTT: Subscribed to ${responseTopic}`);

          // Automatically send check command when MQTT connection is established
          setTimeout(() => {
            // console.log('MQTT: Sending automatic check command after connection');
            const checkMessage = JSON.stringify({
              id: user.device.deviceNumber,
              command: "check"
            });

            const success = mqttService.publish(user.device.deviceNumber, checkMessage);
            if (success) {
              // console.log('MQTT: Automatic check command sent successfully');
              setAction("check");
              setDescription("Getting device status...");
            } else {
              // console.error('MQTT: Failed to send automatic check command');
            }
          }, 1000); // Wait 1 second after connection to ensure everything is ready
        });

        mqttService.on('message', (topic, message) => {
          // console.log(`MQTT: Received message on ${topic}: ${message}`);

          // Handle device response messages (format: deviceNumber/msg)
          if (topic.includes('/msg')) {
            try {
              const data = JSON.parse(message);
              // console.log('MQTT: Parsed device data:', data);

              // Convert the MQTT response format to the expected displayData format
              // MQTT format: {"Lon":"106.88213 E","Lat":"47.93462 N","hum":10,"ver":"1.3.9","rssi":30,"volt":12.4773,"Speed":0,"temp":22,"motion":0,"light":0}
              // Expected format: payload as JSON string

              // Parse coordinates properly to ensure they are numbers
              let latitude = data.Lat;
              let longitude = data.Lon;

              // Handle string coordinates with directional indicators
              if (typeof data.Lat === 'string') {
                latitude = parseFloat(data.Lat.replace(/[^\d.-]/g, ''));
                // Ensure it's a valid number
                if (isNaN(latitude)) {
                  latitude = 0;
                }
              }
              if (typeof data.Lon === 'string') {
                longitude = parseFloat(data.Lon.replace(/[^\d.-]/g, ''));
                // Ensure it's a valid number
                if (isNaN(longitude)) {
                  longitude = 0;
                }
              }

              // console.log('MQTT: Parsed coordinates - Lat:', latitude, 'Lon:', longitude, 'Types:', typeof latitude, typeof longitude);

              const payload = JSON.stringify({
                volt: data.volt,
                temp: data.temp,
                hum: data.hum,
                Lat: latitude,
                Lon: longitude,
                motion: data.motion,
                light: data.light,
                sta: data.volt >= 13.5 ? 1 : 0, // Determine engine status based on voltage
                Speed: data.Speed,
                ver: data.ver,
                rssi: data.rssi
              });

              // Process the response similar to socket.io data-received
              displayData({
                payload: payload,
                ts: new Date(),
                from_client_id: user.device.deviceNumber
              });
            } catch {
              // console.error('MQTT: Error parsing response message:', error);
            }
          }
        });

        mqttService.on('disconnect', () => {
          // console.log('MQTT: Disconnected');
          setMqttConnected(false);
          setMqttConnecting(false);
        });

        mqttService.on('error', () => {
          // console.error('MQTT: Connection error:', error);
          setMqttConnected(false);
          setMqttConnecting(false);

          // Show user-friendly error message
          enqueueSnackbar('Connection failed - please check your network', { variant: 'error' });
        });
      } else {
        // console.error('MQTT: Failed to start connection');
        setMqttConnecting(false);
        enqueueSnackbar('Failed to connect to device', { variant: 'error' });
      }
    } catch {
      // console.error('MQTT: Failed to connect:', error);
      setMqttConnected(false);
      setMqttConnecting(false);
      enqueueSnackbar('Connection error - please try again', { variant: 'error' });
    }
  };

  // MQTT command sending function
  const sendMqttCommand = (command) => {
    if (!mqttConnected || !mqttService.isConnected) {
      // console.error('MQTT: Cannot send command, not connected');

      // If MQTT is connecting, show connecting status instead of error
      if (mqttConnecting && protocol === 'tcp' && user?.device?.deviceNumber) {
        // console.log('MQTT: Connection in progress, queuing command...');
        setAction(command);
        setDescription(`Connecting to your device...`);

        // Set up a one-time listener for connection success
        const handleConnectionSuccess = () => {
          // console.log('MQTT: Connection established, sending queued command');
          mqttService.off('connect', handleConnectionSuccess);
          // Retry the command now that we're connected
          setTimeout(() => sendMqttCommand(command), 500);
        };

        mqttService.on('connect', handleConnectionSuccess);

        // Set up a timeout for the connection attempt (use longer timeout)
        setTimeout(() => {
          if (!mqttConnected) {
            mqttService.off('connect', handleConnectionSuccess);
            enqueueSnackbar('Connection timeout - please check your connection', { variant: 'error' });
            setDescription('');
          }
        }, 15000); // 15 second timeout instead of 2 seconds

        return true; // Return true to prevent immediate error
      }

      // If not connecting, show appropriate error or attempt to reconnect
      if (protocol === 'tcp') {
        // console.log('MQTT not connected for LwM2M protocol, attempting to reconnect...');
        // Try to reconnect
        connectToMqttBroker();
        enqueueSnackbar('Connecting to device, please try again in a moment', { variant: 'info' });
      } else {
        enqueueSnackbar('Device not available for this protocol', { variant: 'warning' });
      }
      return false;
    }

    try {
      const topic = user.device.deviceNumber;
      const message = JSON.stringify({
        id: user.device.deviceNumber,
        command: command
      });

      // console.log(`MQTT: Sending command ${command} to topic ${topic}`);
      // console.log(`MQTT: Message: ${message}`);

      const success = mqttService.publish(topic, message);

      if (success) {
        setAction(command);
        setDescription(`Sending command to your device...`);
        // setTimerVisible(true); // Removed for better bird animation

        if (command.includes("lock")) {
          const lockSound = new Audio(lock);
          lockSound.play();
        }

        // Log command to statistics (async, don't wait for response)
        try {
          const commandType = determineCommandType(command);
          axios.post('/api/admin/statistics/log-command', {
            deviceNumber: user.device.deviceNumber,
            command: command,
            commandType: commandType,
            method: 'MQTT_DIRECT'
          }).catch(statsError => {
            console.log('Statistics logging failed (non-critical):', statsError);
          });
        } catch (statsError) {
          console.log('Statistics logging failed (non-critical):', statsError);
        }

        return true;
      } else {
        enqueueSnackbar('Failed to send command to device', { variant: 'error' });
        return false;
      }
    } catch {
      // console.error('MQTT: Error sending command:', error);
      enqueueSnackbar('Error sending command to device', { variant: 'error' });
      return false;
    }
  };

  // Helper function to determine command type for statistics
  const determineCommandType = (command) => {
    const cmd = command.toLowerCase();
    if (cmd.includes('lock')) return 'lock';
    if (cmd.includes('unlock')) return 'unlock';
    if (cmd.includes('power') || cmd.includes('on') || cmd.includes('off')) return 'power_on';
    if (cmd.includes('location') || cmd.includes('gps')) return 'location';
    if (cmd.includes('status')) return 'status';
    return 'other';
  };

  // Toggle-style handlers
  const handleLockToggle = (event) => {
    // Capture button position for bird animation
    if (event && event.currentTarget) {
      const rect = event.currentTarget.getBoundingClientRect();
      setButtonPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      });
    }

    // Start bouncing animation
    setBouncingButton('lock');
    setTimeout(() => setBouncingButton(null), 3000); // Stop bouncing after 3 seconds

    if (isLocked) {
      setMessage(t("home.door_open"));
      handleDeviceControl("unlock");
      setIsLocked(false); // Update state immediately for UI responsiveness
    } else {
      setMessage(t("home.door_close"));
      handleDeviceControl("lock");
      setIsLocked(true); // Update state immediately for UI responsiveness
    }
  };

  // Engine button hold handlers
  const handleEngineMouseDown = (event) => {
    // Prevent default to avoid any interference
    event.preventDefault();

    // Capture button position for bird animation
    const rect = event.currentTarget.getBoundingClientRect();
    setButtonPosition({
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    });

    setIsEngineHolding(true);
    setEngineHoldProgress(0);

    const startTime = Date.now();
    const duration = 3000; // 3 seconds

    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      setEngineHoldProgress(progress * 100);

      if (progress >= 1) {
        // Hold completed - execute command
        clearInterval(timer);
        setIsEngineHolding(false);
        setEngineHoldProgress(0);

        // Start bouncing animation
        setBouncingButton('power');
        setTimeout(() => setBouncingButton(null), 3000); // Stop bouncing after 3 seconds

        if (isEngineOn) {
          setMessage(t("home.turn_off"));
          handleDeviceControl("untar");
          setIsEngineOn(false);
        } else {
          setMessage(t("home.turn_on"));
          handleDeviceControl("as");
          setIsEngineOn(true);
        }
      }
    }, 16); // ~60fps

    setEngineHoldTimer(timer);
  };

  const handleEngineMouseUp = (event) => {
    // Prevent default to avoid any interference
    if (event) {
      event.preventDefault();
    }

    // Cancel hold if released early
    if (engineHoldTimer) {
      clearInterval(engineHoldTimer);
      setEngineHoldTimer(null);
    }
    setIsEngineHolding(false);
    setEngineHoldProgress(0);
  };



  const handleLocationToggle = (event) => {
    // Capture button position for bird animation
    if (event && event.currentTarget) {
      const rect = event.currentTarget.getBoundingClientRect();
      setButtonPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      });
    }

    // Start bouncing animation
    setBouncingButton('location');
    setTimeout(() => setBouncingButton(null), 3000); // Stop bouncing after 3 seconds

    if (viewMap) {
      // Hide location
      setViewMap(false);
    } else {
      // Show location - get current status first
      handleLocation(true);
    }
  };


  const handleOn1 = () => {
    setMessage(t("home.on1"));
    handleDeviceControl("on1");
  };
  const handleOn2 = () => {
    setMessage(t("home.on2"));
    handleDeviceControl("on2");
  };
  const handleOff1 = () => {
    setMessage(t("home.off1"));
    handleDeviceControl("off1");
  };
  const handleOff2 = () => {
    setMessage(t("home.off2"));
    handleDeviceControl("off2");
  };

  // Handlers for power button (simplified)
  const handlePowerClick = () => {
    handleOff2(); // Keep the original power functionality
  };

  // Handlers for new dedicated buttons
  const handleScheduleClick = () => {
    setSchedulerDialogOpen(true);
  };

  const handleSchedulerClose = () => {
    setSchedulerDialogOpen(false);
  };

  const handleGpsHistoryClick = (event) => {
    // Capture button position for bird animation
    if (event && event.currentTarget) {
      const rect = event.currentTarget.getBoundingClientRect();
      setButtonPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      });
    }

    // Start bouncing animation (same as map button)
    setBouncingButton('gpsHistory');
    setTimeout(() => setBouncingButton(null), 3000); // Stop bouncing after 3 seconds

    // Log for debugging purposes
    console.log('GPS History clicked - temporarily using map behavior');
    
    // Temporarily use the same behavior as the Map button
    // Send a check command to get device status
    setMessage(t("words.get_status"));
    handleDeviceControl("check");
    
    // In the future, this will navigate to the GPS history page
    // navigate('/gps-history');
  };

  const handleMirrorControlClick = () => {
    // Send the mirror control command to the device
    console.log('Mirror Control clicked - sending mirror command');
    handleDeviceControl("mirror");
  };

  const handleLocation = (mapView = true) => {
    if (user.status === "expired") {
      enqueueSnackbar(t("words.expired_message"), { variant: "error" });
      return false;
    }

    // For LwM2M protocol, check if MQTT is connected before proceeding
    if (protocol === 'tcp' && !mqttConnected && !mqttConnecting) {
      // console.log('MQTT not connected for LwM2M protocol, attempting to connect first...');
      connectToMqttBroker();
      return false;
    }

    if (!viewMap) {
      setMessage(t("words.get_status"));
      setCommand("check");
      confirmed("check");
      if (mapView) {
        setTimeout(() => {
          setViewMap(!viewMap);
        }, 3000);
      }
    } else setViewMap(!viewMap);
  };
  const displayData = (data) => {
    const payloadString = data.payload.replace(" N", "").replace(" E", "");

    if (isJson(payloadString)) {
      const payload = JSON.parse(payloadString);
      const ts = new Date(data.ts);
      if (payload.motion === 1) {
        setMotionTime(`${ts.getHours()}:${ts.getMinutes()}:${ts.getSeconds()}`);
      }
      if (payload.sta === 0) {
        setColor("red");
      }
      if (payload.sta === 1) {
        setColor("green");
        const engineSound = new Audio(engine);
        engineSound.play().catch(() => {
          // Audio play failed - user interaction required
        });
      }

      if (payload.volt >= 13.5) {
         const engineSound = new Audio(engine);
         engineSound.play().catch(() => {
           // Audio play failed - user interaction required
         });
         setColor("green");
      } else {
         setColor("gray");
      }

      const carStatus = {
        volt: payload.volt,
        temp: payload.temp,
        hum: payload.hum,
        Lat: typeof payload.Lat === 'string' ? parseFloat(payload.Lat) : payload.Lat,
        Lon: typeof payload.Lon === 'string' ? parseFloat(payload.Lon) : (payload.Lon || payload.lon),
        motion: payload.motion,
        light: payload.light,
        sta: payload.sta,
        speed: payload.Speed,
        ver: payload.ver,
        backlight: payload?.s1 || 0,
        rssi: payload.rssi,
      };
      setDeviceStatus(carStatus);

      // Update toggle states based on device response
      if (payload.sta !== undefined) {
        setIsEngineOn(payload.sta === 1);
      }

      // Update lock state based on recent action
      if (action === "lock") {
        setIsLocked(true);
      } else if (action === "unlock") {
        setIsLocked(false);
      }

      // setTimerVisible(false); // Hide timer when data arrives - REMOVED for better bird animation
    } else {
      const status = data.payload;
      if (status === "aslaa!") {
        setColor("green");
        play();
      } else if (status === "untarlaa!") {
        setColor("red");
      } else {
        // console.log('Something wrong in data');
      }
    }
    // console.log("Incoming data:", data); // Log incoming data
    // console.log("Current color state:", color); // Log current color state
  };

  const isJson = (data) => {
    try {
      JSON.parse(data);
    } catch {
      return false;
    }
    return true;
  };
  const confirmed = (preStatus) => {
    if (
      user.device === null ||
      (user?.status === "trial" && user?.remainDays <= 0)
    ) {
      setCheckedPincode(true);
      setLoading(true);

      setAction(command);
      setDescription(`${command} was success`);

      setTimeout(() => {
        setLoading(false);
        const engineStatus = command === "as" ? 1 : (command === "untar" ? 0 : (isEngineOn ? 1 : 0));
        const payload = JSON.stringify({
          volt: fShortenNumber(Math.random() * 12),
          temp: fShortenNumber(Math.random() * 60),
          hum: fShortenNumber(Math.random() * 50 + 40),
          Lat: 47.918918911176014,
          Lon: 106.91759599276008,
          motion: Math.floor(Math.random()),
          light: Math.floor(Math.random()),
          sta: engineStatus,
          rel1: Math.floor(Math.random()),
          rel2: Math.floor(Math.random()),
          backlight: 1,
        });
        const mockdata = {
          ts: new Date(),
          payload,
        };
        displayData(mockdata);
      }, 3000);
    } else {
      setCheckedPincode(true);
      setLoading(true);
      setDescription("");

      const commandToSend = preStatus || command;
      // console.log("Command being sent:", commandToSend);
      // console.log("Protocol selected:", protocol);

      // Check if LwM2M protocol is selected (tcp)
      if (protocol === 'tcp') {
        // console.log("Using MQTT for LwM2M protocol");

        // Use MQTT for LwM2M protocol
        const success = sendMqttCommand(commandToSend);

        if (success) {
          // MQTT command sent successfully, timer and description are set in sendMqttCommand
          setTimeout(() => {
            setLoading(false);
          }, 3000);
        } else {
          // Command failed, fallback to HTTP or show error
          setLoading(false);
          enqueueSnackbar("Command failed, please check connection", {
            variant: "error",
          });
        }
      } else {
        // console.log("Using HTTP API for XMPP protocol");

        // Use HTTP API for XMPP protocol (original logic)
        // const requestPayload = {
        //   deviceNumber: user?.device?.deviceNumber,
        // };
        // console.log("Sending to API with payload:", requestPayload);

        axios
          .post(`/api/device/control/:${commandToSend}`, {
            deviceNumber: user?.device?.deviceNumber,
          })
          .then((res) => {
            if (res.data.success) {
              setAction(commandToSend);
              setDescription(
                `Command sent successfully, please wait...`
              );
              // setTimerVisible(true); // Show timer when command is sent - REMOVED for better bird animation
              if (command.includes("lock")) {
                const lockSound = new Audio(lock);
                lockSound.play().catch(() => {
                  // Audio play failed - user interaction required
                });
              }
            } else if (res.data.err) {
              setAction("");
              if (typeof res.data.err === "object")
                setDescription(
                  `code = ${res.data.err.code}, host= ${res.data.err.address}, port =  ${res.data.err.port}`
                );
              if (typeof res.data.err === "string") {
                setDescription(res.data.err);
                setLoading(false);
                enqueueSnackbar(res.data.err, { variant: "error" });
              }
            }
            setTimeout(() => {
              setLoading(false);
            }, 3000);
          })
          .catch(() => {
            enqueueSnackbar("Please check your connection or status", {
              variant: "error",
            });
            setLoading(false);
          });
      }
    }
  };

  const handleDeviceControl = (cmd) => {
    if (user.status === "expired") {
      enqueueSnackbar(t("words.expired_message"), { variant: "error" });
      return false;
    }
    setCommand(cmd);

    // Skip pincode verification - directly execute command
    setCheckedPincode(true);
    confirmed(cmd);
  };

  const isMobile = useResponsive("down", "sm");

  useEffect(() => {

    fetchSimStatus();
    let mounted = true;
    if (user) {
      if (user.device) {
        const uix = user.device.uix;
        if (uix.includes("Car")) {
          setChangeicon(false);
        } else if (uix.includes("GPS")) {
          navigate("/log-gps");
        } else {
          setChangeicon(true);
        }
      }

      // Connect to MQTT if LwM2M protocol is selected
      if (protocol === 'tcp' && user.device?.deviceNumber) {
        // console.log('LwM2M protocol detected, connecting to MQTT...');
        // Add a small delay to ensure component is fully mounted
        setTimeout(() => {
          if (mounted) {
            connectToMqttBroker();
          }
        }, 500);
      }

      const socket = io.connect(`${HOST_API}`, {
        transports: ["polling", "websocket"],
      });
      socket.on("connect", () => {
        // console.log('connected to host');
        socket.emit("logined", user);
      });
      socket.on("disconnect", () => {
        // console.log("disconnected from the host");
      });

      if (user?.device?.type === "4g") {
        setCheckOnline(true);
        axios
          .post(`/api/device/checkline`, {
            deviceNumber: user?.device?.deviceNumber,
          })
          .then((res) => {
            if (!mounted) return;
            if (res.data.status === "online") {
              setOnline(true);
              setColor("red");

              // Only call handleLocation for non-LwM2M protocols or when MQTT is connected
              if (protocol !== 'tcp') {
                // For XMPP protocol, use handleLocation immediately
                handleLocation(false);
              } else {
                // For LwM2M protocol, wait for MQTT connection or use a delay
                // console.log('LwM2M protocol detected, skipping immediate location check - will be handled by MQTT auto-check');
              }
            } else {
              setOnline(false);
              setColor("yellow");
            }
          })
          .finally(() => {
            if (!mounted) return;
            setCheckOnline(false);
          });
      }

      socket.on("data-received", (data) => {
        if (!mounted) return;
        if (data.payload && data.from_client_id === user.device.deviceNumber) {
          displayData(data);
        }
      });

      if (status !== null && status.sta >= 0) {
        setDeviceStatus(status);

        if (status.sta === 1) {
          setColor("green");
          setIsEngineOn(true);
        } else {
          setIsEngineOn(false);
        }
      }

      return () => {
        try {
          mounted = false;
          if (socket.disconnect()) {
            socket.close();
          }
          // Disconnect MQTT if connected
          if (mqttConnected && mqttService.isConnected) {
            mqttService.disconnect();
            setMqttConnected(false);
          }
          // Clear engine hold timer if active
          if (engineHoldTimer) {
            clearInterval(engineHoldTimer);
            setEngineHoldTimer(null);
          }
        } catch (err) {
          // console.log(err);
        }
      };

    }
  }, [deviceNumber, protocol]);

  // Countdown timer effect - REMOVED for better bird animation
  // useEffect(() => {
  //   if (isTimerVisible) {
  //     const timer = setInterval(() => {
  //       setCountdown((prev) => {
  //         if (prev <= 1) {
  //           clearInterval(timer);
  //           return 0;
  //         }
  //         return prev - 1;
  //       });
  //     }, 1000);
  //     return () => clearInterval(timer);
  //   }
  // }, [isTimerVisible]);

  const getSimCardColor = () => {
    if (!simStatus.expiredDate) return "inherit";

    // Determine the delimiter used in the date string
    let delimiter = '/';
    if (simStatus.expiredDate.includes('-')) {
      delimiter = '-';
    }

    // Split the date string based on the delimiter
    const dateParts = simStatus.expiredDate.split(delimiter);

    // Log the split parts for debugging
    // console.log("Date Parts:", dateParts);

    // Ensure the date string has exactly 3 parts
    if (dateParts.length !== 3) {
      // console.error("Invalid date format:", simStatus.expiredDate);
      return "inherit";
    }

    // Parse the date components as integers
    let [part1, part2, part3] = dateParts.map(part => parseInt(part, 10));

    // **Assumption**: The format is 'DD-MM-YY'
    let day = part1;
    let month = part2;
    let year = part3;

    // Adjust the year to be in the 2000s if it's less than 100
    if (year < 100) {
      year += 2000;
    }

    // Validate the month
    if (month < 1 || month > 12) {
      // console.error("Invalid month value:", month);
      return "inherit";
    }

    // Validate the day
    if (day < 1 || day > 31) { // Further validation can be added based on the month
      // console.error("Invalid day value:", day);
      return "inherit";
    }

    // Construct the Date object (Note: Months are 0-indexed in JavaScript)
    const expiredDate = new Date(year, month - 1, day);



    // Check if the constructed date is valid
    if (isNaN(expiredDate.getTime())) {
      // console.error("Constructed date is invalid:", expiredDate);
      return "inherit";
    }

    const currentDate = new Date();

    // Calculate the difference in time (milliseconds)
    const diffTime = expiredDate - currentDate;

    // Convert the difference from milliseconds to days
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // console.log("Difference in Days:", diffDays);

    // Determine the color based on the difference in days
    if (diffDays < 0) {
      return "red"; // Expired
    } else if (diffDays <= 7) {
      return "yellow"; // Expiring soon
    } else {
      return "inherit"; // Not expiring soon
    }
  };

   // Step 2: Check pin code right when the component mounts or user changes
   useEffect(() => {
    // If user has no pin code, show the setup
    if (user && !user.pinCode) {
      setShowPincodeSetup(true);
    }
  }, [user]);

   // Step 3: onSuccess callback after the user sets a new pin
   const handleNewPinSuccess = (newPin) => {
    // We assume your backend call was successful
    // 1) Hide the dialog
    setShowPincodeSetup(false);

    // 2) Possibly refresh user or store the new pin in local context
    // For example, if your `user` is stored in a global store or via `initialize`
    // you can do something like:
    const updatedUser = { ...user, pinCode: newPin };
    initialize(updatedUser);

    // now user has a pin code
  };
  return (
    <Page title="car remote system">
      <Layout />

      {/* MQTT Connection Debugger - Remove this after debugging */}
      {/* {process.env.NODE_ENV === 'development' && <MqttDebugger />} */}

      <PinCodeSetupDialog
        open={showPincodeSetup}
        onClose={() => setShowPincodeSetup(false)}
        onSuccess={handleNewPinSuccess}
        phoneNumber={user?.phoneNumber} // pass the user’s phoneNumber
      />

      {/* 5) The confirm pin code dialog you already have */}
      <PinCodeConfirm
        open={!checkedPincode}
        onModalClose={() => {
          setCheckedPincode(true);
        }}
        onSuccess={confirmed}
      />
      <SeasonalAnimation />
      <Container maxWidth={"lg"} sx={{
        pt: { xs: 8, md: 11 },
        mt: { xs: 6, md: 8 },
        px: 3, // 24px horizontal padding for breathing room
        background: 'transparent', // Transparent to show premium dark background
        backdropFilter: "none", // Remove blur for cleaner appearance
        borderRadius: "20px",
        border: 'none', // No borders for minimal design
        boxShadow: 'none', // Remove shadow for seamless integration
        overflow: "hidden",
      }}>
        {loading && (
          <FindingScreen
            message={message}
            description={description}
            buttonPosition={buttonPosition}
            showBirdAnimation={true}
          />
        )}
        <Grid
          container
          spacing={4}
          sx={{
            // backgroundImage: "url(images/back.svg)",
            background: "transparent",
            backdropFilter: "blur(6px)",
            backgroundColor: "rgba(255,255,255,0.04)",
            p: 3,
            borderRadius: 2,
            boxShadow: 3
          }}
        >
          <Grid
            item
            xs={12}
            md={6}
            textAlign={"center"}
            sx={{
              mt: iconChange ? "50px" : "",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: { xs: 400, md: 500, lg: 600 },
              position: 'relative',
              backgroundColor: "rgba(255,255,255,0.05)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              boxShadow: 4,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                width: '100%',
                height: '100%',
                // Add margin or shift to create space between car and right indicators
                mr: { xs: '8%', sm: '6%', md: '4%' }, // Reduced from 10/8/5% to 8/6/4%
                // Alternative approach using position adjustment
                position: 'relative',
                left: { xs: '-3%', sm: '-2.5%', md: '-1.5%' }, // Reduced from -5/-4/-2.5% to -3/-2.5/-1.5%
              }}
            >
              {!iconChange &&
                (user?.device?.uix?.toLowerCase()?.includes("carv1") ||
                  user.device === null) && (
                  <Car
                    sx={{
                      cursor: "pointer",
                    }}
                    device={user.device}
                    color={color}
                    action={action}
                    status={deviceStatus}
                  />
                )}
              {!iconChange && user?.device?.uix?.includes("Car2") && (
                <CarSide
                  sx={{ cursor: "pointer" }}
                  device={user.device}
                  color={color}
                  action={action}
                  status={deviceStatus}
                />
              )}
              {iconChange && (
                <Box>
                  <ChipIcon
                    sx={{ width: { xs: "60%", sm: "70%" }, cursor: "pointer" }}
                    device={user.device}
                    color={getSimCardColor()}
                    action={action}
                    status={deviceStatus}
                  />
                </Box>
              )}
            </Box>
            {isMobile && (
              <Stack 
                gap={1.5} 
                position={"absolute"} 
                top={"50%"}  // Changed from "10%" to "50%" to center vertically
                left={"3%"}
                sx={{
                  transform: "translateY(-50%)",  // Added to ensure perfect vertical centering
                  zIndex: 10
                }}
              >
                {/* Signal Strength Indicator */}
                <Stack gap={0.5} direction={"row"} alignItems={"center"}>
                  {!checkOnline ? (
                    <Stack direction="row" alignItems="center" gap={0.5}>
                      <Iconify
                        icon={online ? "bx:signal-5" : "bx:no-signal"}
                        width={14}
                        height={14}
                        sx={{
                          color: deviceStatus?.rssi > 25 ? 'green' :
                                 deviceStatus?.rssi > 15 ? 'yellow' :
                                 deviceStatus?.rssi > 5 ? 'orange' : 'red'
                        }}
                      />
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontSize: '0.7rem',
                          lineHeight: 1.2,
                          fontFamily: '"Roboto Mono", monospace',
                          fontWeight: 500,
                          letterSpacing: '0.05em',
                          color: '#f0f0f0'
                        }}
                      >
                        {deviceStatus?.rssi ? `${deviceStatus.rssi} dBm` : "N/A"}
                      </Typography>
                    </Stack>
                  ) : (
                    <Iconify
                      icon={"eos-icons:three-dots-loading"}
                      width={14}
                      height={14}
                    />
                  )}
                </Stack>

                {/* Device Temperature Indicator */}
                <Stack gap={0.5} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"carbon:temperature-max"}
                    width={14} // Smaller compact size
                    height={14}
                  />
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontSize: '0.7rem',
                      lineHeight: 1.2,
                      fontFamily: '"Roboto Mono", monospace',
                      fontWeight: 500,
                      letterSpacing: '0.05em',
                      color: '#f0f0f0'
                    }}
                  >
                    {deviceStatus?.temp
                      ? `${fShortenNumber(deviceStatus?.temp - 20)}°C`
                      : "N"}
                  </Typography>
                </Stack>

                {/* Humidity Indicator */}
                <Stack gap={0.5} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"carbon:humidity"}
                    width={14} // Smaller compact size
                    height={14}
                  />
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontSize: '0.7rem',
                      lineHeight: 1.2,
                      fontFamily: '"Roboto Mono", monospace',
                      fontWeight: 500,
                      letterSpacing: '0.05em',
                      color: '#f0f0f0'
                    }}
                  >
                    {deviceStatus?.hum || "N"}%
                  </Typography>
                </Stack>

                {/* Battery Voltage Indicator */}
                <Stack gap={0.5} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"la:car-battery"}
                    width={14} // Smaller compact size
                    height={14}
                    sx={{ color: deviceStatus?.volt > 11 ? "white" : "red" }}
                  />
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontSize: '0.7rem',
                      lineHeight: 1.2,
                      fontFamily: '"Roboto Mono", monospace',
                      fontWeight: 500,
                      letterSpacing: '0.05em',
                      // Preserve voltage color coding
                      color: deviceStatus?.volt > 13 ? '#00ff00' :
                             deviceStatus?.volt > 11 ? '#ffff00' : '#ff0000'
                    }}
                  >
                    {deviceStatus?.volt
                      ? `${parseFloat(deviceStatus.volt).toFixed(1)}V`
                      : "N/A"}
                  </Typography>
                </Stack>

                {/* SIM Card Status Indicator */}
                <Stack gap={0.5} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"mdi:sim"}
                    width={14} // Smaller compact size
                    height={14}
                    onClick={checkSimcard}
                    sx={{ color: getSimCardColor() }}
                  />
                  <Stack>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.65rem',
                        lineHeight: 1.1,
                        fontFamily: '"Roboto Mono", monospace',
                        fontWeight: 400,
                        letterSpacing: '0.05em',
                        color: '#f0f0f0'
                      }}
                    >
                      {simStatus.balance || "N/A"}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.65rem',
                        lineHeight: 1.1,
                        fontFamily: '"Roboto Mono", monospace',
                        fontWeight: 400,
                        letterSpacing: '0.05em',
                        color: '#f0f0f0'
                      }}
                    >
                      {simStatus.expiredDate || "N/A"}
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>
            )}
            {isMobile && (
              <Stack
                gap={1.5}
                position={"absolute"}
                top={"50%"}  // Changed from "8%" to "50%" to center vertically
                right={"2%"}
                sx={{
                  alignItems: 'flex-start',
                  zIndex: 10,
                  minWidth: '80px',
                  transform: "translateY(-50%)",  // Added to ensure perfect vertical centering
                }}
              >
                {/* Weather Temperature Indicator */}
                <Stack gap={0.5} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"mdi:home-temperature-outline"}
                    width={14}
                    height={14}
                    sx={{ color: '#f0f0f0', flexShrink: 0 }}
                  />
                  <WeatherDisplay simpleMode={true} />
                </Stack>

                {/* GPS Status Indicator */}
                <Stack gap={0.5} direction={"row"} alignItems={"center"}>
                  <SatelliteAltIcon
                    sx={{
                      width: 14,
                      height: 14,
                      flexShrink: 0,
                      color: deviceStatus?.Lat && deviceStatus?.Lat > 0
                        ? "#10b981"
                        : deviceStatus?.Lat && parseInt(deviceStatus?.Lat) === 0
                          ? "#ef4444"
                          : "#06b6d4"
                    }}
                  />
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontSize: '0.7rem',
                      lineHeight: 1.2,
                      fontFamily: '"Roboto Mono", monospace',
                      fontWeight: 400,
                      letterSpacing: '0.05em',
                      color: '#f0f0f0',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    GPS
                  </Typography>
                </Stack>

                {/* License Remaining Days Indicator */}
                <Stack gap={0.5} direction="row" alignItems="center">
                  <Iconify
                    icon="mdi:calendar-clock"
                    width={14}
                    height={14}
                    sx={{
                      color: user?.status === 'expired' ? '#ef4444' :
                             (user?.remainDays <= 7 ? '#f59e0b' : '#14b8a6'),
                      flexShrink: 0
                    }}
                  />
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontSize: '0.7rem',
                      lineHeight: 1.2,
                      fontFamily: '"Roboto Mono", monospace',
                      fontWeight: 500,
                      letterSpacing: '0.05em',
                      color: '#f0f0f0',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {user?.remainDays
                      ? `${Math.floor(user.remainDays / (1000 * 60 * 60 * 24))}D`
                      : 'N/A'
                    }
                  </Typography>
                </Stack>

                {/* Version Information Indicator */}
                <Stack gap={0.5} direction={'row'} alignItems={'center'}>
                  <InfoOutlined
                    sx={{
                      width: 14,
                      height: 14,
                      color: '#737373',
                      flexShrink: 0
                    }}
                  />
                  <Typography
                    sx={{
                      fontSize: '0.7rem',
                      lineHeight: 1.2,
                      fontFamily: '"Roboto Mono", monospace',
                      fontWeight: 500,
                      letterSpacing: '0.05em',
                      color: '#f0f0f0',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {formatVersion(deviceStatus?.ver) || "1.0"}
                  </Typography>
                </Stack>

                {/* Speed Indicator - Replacing MQTT indicator */}
                {protocol === 'tcp' && (
                  <Stack gap={0.5} direction={'row'} alignItems={'center'}>
                    <Iconify
                      icon="mdi:speedometer"
                      width={14}
                      height={14}
                      sx={{
                        color: '#10b981',
                        flexShrink: 0
                      }}
                    />
                    <Typography
                      variant="subtitle2"
                      sx={{
                        fontSize: '0.7rem',
                        lineHeight: 1.2,
                        fontFamily: '"Roboto Mono", monospace',
                        fontWeight: 500,
                        letterSpacing: '0.05em',
                        color: '#f0f0f0',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {deviceStatus?.Speed ? `${deviceStatus.Speed} km/h` : '0 km/h'}
                    </Typography>
                  </Stack>
                )}
              </Stack>
            )}
          </Grid>

          <Grid item xs={12} md={6}>
            <Stack
              gap={{ xs: 2, md: 4 }}
              justifyContent="space-between"
              sx={{ height: "100%" }}
            >
              {!isMobile && (
                <>
                  <Typography variant="h4" className="car-status-display" sx={{ mt: 3 }}>
                    {t("home.informations")}
                  </Typography>
                  <Divider />
                  <Box
                    sx={{
                      display: "grid",
                      columnGap: 3,
                      rowGap: 3,
                      gridTemplateColumns: {
                        xs: "repeat(2, 1fr)",
                        sm: "repeat(3, 1fr)",
                        md: "repeat(3, 1fr)",
                        lg: "repeat(4, 1fr)"
                      },
                      '@media (min-width: 1200px)': {
                        gridTemplateColumns: "repeat(4, 1fr)",
                        columnGap: 4
                      }
                    }}
                  >
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      {!checkOnline ? (
                        <Stack alignItems="center" gap={1}>
                          <Iconify
                            icon={online ? "bx:signal-5" : "bx:no-signal"}
                            width={30}
                            height={30}
                          />
                          <Typography variant="body2" className="digital-font signal-display">
                            {deviceStatus?.rssi ?`${deviceStatus.rssi} dBm` : "N/A"}
                          </Typography>
                        </Stack>
                      ) : (
                        <Iconify
                          icon={"eos-icons:three-dots-loading"}
                          width={30}
                          height={30}
                        />
                      )}
                    </Card>

                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"carbon:temperature-max"}
                        width={30}
                        height={30}
                      />
                      <Typography variant="body2" className="temperature-display automotive-font" sx={{ mt: 1 }}>
                        {deviceStatus?.temp
                          ? `${fShortenNumber(deviceStatus?.temp - 20)}°C`
                          : " "}
                      </Typography>
                    </Card>
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"carbon:humidity"}
                        width={30}
                        height={30}
                      />
                      <Typography variant="body2" className="automotive-font" sx={{ mt: 1 }}>
                        {deviceStatus?.hum || "N"}%
                      </Typography>
                    </Card>
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"la:car-battery"}
                        width={30}
                        height={30}
                        sx={{ color: deviceStatus?.volt > 11 ? "inherit" : "red" }}
                      />
                      <Typography
                        variant="body2"
                        className={`voltage-display ${
                          deviceStatus?.volt > 13 ? 'high' :
                          deviceStatus?.volt > 11 ? 'medium' : 'low'
                        }`}
                        sx={{ mt: 1 }}
                      >
                        {`${deviceStatus?.volt || "N/A"}V`}
                      </Typography>
                    </Card>
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"mdi:sim"}
                        width={30}
                        height={30}
                        sx={{ color: getSimCardColor() }}
                      />
                      <Stack spacing={0.5} sx={{ mt: 1 }}>
                        <Typography variant="body2" className="technical-font">
                          Balance: {simStatus.balance || "N/A"}
                        </Typography>
                        <Typography variant="body2" className="technical-font">
                          Expires: {simStatus.expiredDate || "N/A"}
                        </Typography>
                      </Stack>
                    </Card>

                    {/* GPS Status Card */}
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <SatelliteAltIcon
                        sx={{
                          width: 30,
                          height: 30,
                          color: deviceStatus?.Lat && deviceStatus?.Lat > 0
                            ? "info"
                            : deviceStatus?.Lat && parseInt(deviceStatus?.Lat) === 0
                              ? "red"
                              : "info"
                        }}
                      />
                      <Typography variant="body2" className="status-indicator" sx={{ mt: 1 }}>
                        GPS Status
                      </Typography>
                    </Card>

                    {/* Remaining Days Card */}
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon="mdi:calendar-clock"
                        width={30}
                        height={30}
                        sx={{
                          color: user?.status === 'expired'
                            ? 'red'
                            : (user?.remainDays <= 7 ? 'yellow' : 'inherit')
                        }}
                      />
                      <Typography variant="body2" className="digital-font-small" sx={{ mt: 1 }}>
                        {user?.remainDays
                          ? `${Math.floor(user.remainDays / (1000 * 60 * 60 * 24))}D`
                          : 'N/A'
                        }
                      </Typography>
                    </Card>

                    {/* Version Card */}
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <InfoOutlined sx={{ width: 30, height: 30 }} />
                      <Typography variant="body2" className="technical-font" sx={{ mt: 1 }}>
                        {deviceStatus?.ver !== undefined
                          ? `V${formatVersion(deviceStatus.ver)}`
                          : "V1.0"}
                      </Typography>
                    </Card>



                  </Box>
                </>
              )}

              <Divider sx={{ mt: { xs: 2, md: iconChange ? "70px" : "30px" } }} />
              {user.status === "expired" && (
                <Alert
                  severity="warning"
                  sx={{ backgroundColor: "grey.50024" }}
                >
                  {t("words.license_has_expired")} <br />
                  {t("words.get_new_key")}
                </Alert>
              )}
              {iconChange && (
                <Box sx={{ flexGrow: 1 }}>
                  <DeviceController
                    flexGrow={1}
                    onTopLeft={handleOn1}
                    onTopRight={handleOn2}
                    onBottomLeft={handleOff1}
                    onBottomRight={handleOff2}
                    onCenter={handleLocation}
                    // Additional button handlers
                    onSchedule={handleScheduleClick}
                    onGpsHistory={handleGpsHistoryClick}
                    onMirrorControl={handleMirrorControlClick}
                    topLeftContent={
                      <Stack alignItems={"center"} justifyContent={"center"}>
                        <Iconify
                          icon={"fontisto:power"}
                          width={28}
                          height={28}
                        />
                        <Typography variant="caption">
                          {t("home.on1")}
                        </Typography>
                      </Stack>
                    }
                    topRightContent={
                      <Stack alignItems={"center"} justifyContent={"center"}>
                        <Iconify
                          icon={"fontisto:power"}
                          width={28}
                          height={28}
                        />
                        <Typography variant="caption">
                          {t("home.on2")}
                        </Typography>
                      </Stack>
                    }
                    bottomLeftContent={
                      <Stack alignItems={"center"} justifyContent={"center"}>
                        <Iconify
                          icon={"carbon:flash-off"}
                          width={28}
                          height={28}
                          sx={{ color: "red" }}
                        />
                        <Typography variant="caption" sx={{ color: "red" }}>
                          {t("home.off1")}
                        </Typography>
                      </Stack>
                    }
                    bottomRightContent={
                      <ScrollablePowerButton
                        onPowerClick={handlePowerClick}
                        powerLabel={t("home.off2")}
                        powerIcon="carbon:flash-off"
                        powerColor="red"
                        disabled={loading}
                      />
                    }
                    centerContent={
                      <Iconify
                        icon={`fluent:location${viewMap ? "-off" : ""
                          }-20-filled`}
                        width={40}
                        height={40}
                      />
                    }
                    // Additional smaller button content
                    scheduleContent={
                      <ScheduleButton
                        onClick={handleScheduleClick}
                        disabled={loading}
                        label={t("words.schedule", "Schedule")}
                        tooltip="Open command scheduler"
                      />
                    }
                    gpsHistoryContent={
                      <GpsHistoryButton
                        onClick={handleGpsHistoryClick}
                        disabled={loading}
                        label="History"
                        tooltip="View GPS location history"
                      />
                    }
                    mirrorControlContent={
                      <MirrorControlButton
                        onClick={handleMirrorControlClick}
                        disabled={loading}
                        label="Mirror"
                        tooltip="Control side mirrors"
                      />
                    }
                  />
                </Box>
              )}
              {!iconChange && (
                <Box sx={{
                  flexGrow: 1,
                  display: 'flex',
                  justifyContent: 'center',
                  mt: 4,
                  // Perfect centering between left and right indicators
                  mx: {
                    xs: '18%',  // Account for indicator space on mobile (320px-480px)
                    sm: '16%',  // Slightly less margin on small tablets
                    md: '8%'    // Minimal margin on desktop
                  },
                  // Ensure adequate spacing from indicators
                  '@media (max-width: 360px)': {
                    mx: '20%' // Extra margin on very small screens
                  }
                }}>
                  <Stack
                    direction="row"
                    spacing={{ xs: 4, sm: 6, md: 8 }} // Responsive spacing for better fit
                    alignItems="flex-start"
                    sx={{
                      justifyContent: 'center',
                      width: '100%' // Ensure full width usage within margins
                    }}
                  >
                    {/* Column 1: Combined Lock/Mirror Button */}
                    <Stack direction="column" spacing={3} alignItems="center"> {/* Increased vertical spacing */}
                      {/* ScrollableLockButton - combines Lock/Unlock and Mirror Control */}
                      <motion.div
                        animate={bouncingButton === 'lockMirror' ? {
                          y: [0, -8, 0, -6, 0, -4, 0],
                          scale: [1, 1.05, 1, 1.03, 1, 1.02, 1],
                          rotate: [0, 1, 0, -1, 0]
                        } : {}}
                        transition={{
                          duration: 0.6,
                          repeat: bouncingButton === 'lockMirror' ? Infinity : 0,
                          ease: "easeInOut"
                        }}
                      >
                        <ScrollableLockButton
                          onLockClick={(event) => {
                            // Capture button position for bird animation - use the button element
                            const buttonElement = event.target.closest('[role="button"]') || event.currentTarget;
                            if (buttonElement) {
                              const rect = buttonElement.getBoundingClientRect();
                              setButtonPosition({
                                x: rect.left + rect.width / 2,
                                y: rect.top + rect.height / 2
                              });
                            }

                            // Start bouncing animation
                            setBouncingButton('lockMirror');
                            setTimeout(() => setBouncingButton(null), 3000);

                            // Execute the lock toggle logic directly
                            if (isLocked) {
                              setMessage(t("home.door_open"));
                              handleDeviceControl("unlock");
                              setIsLocked(false);
                            } else {
                              setMessage(t("home.door_close"));
                              handleDeviceControl("lock");
                              setIsLocked(true);
                            }
                          }}
                          onMirrorClick={(event) => {
                            // Capture button position for bird animation - use the button element
                            const buttonElement = event.target.closest('[role="button"]') || event.currentTarget;
                            if (buttonElement) {
                              const rect = buttonElement.getBoundingClientRect();
                              setButtonPosition({
                                x: rect.left + rect.width / 2,
                                y: rect.top + rect.height / 2
                              });
                            }

                            // Start bouncing animation
                            setBouncingButton('lockMirror');
                            setTimeout(() => setBouncingButton(null), 3000);

                            // Execute the mirror control function
                            handleMirrorControlClick();
                          }}
                          isLocked={isLocked}
                          lockLabel={isLocked ? t("home.unlock") : t("home.lock")}
                          mirrorLabel={t("home.mirror")}
                          lockIcon="material-symbols:lock"
                          mirrorIcon="mdi:car-side-mirror"
                          colorScheme={{
                            lock: buttonColors.lock,
                            mirror: buttonColors.mirror
                          }}
                          size={85} // Slightly reduced for better mobile layout
                          disabled={loading}
                          showScrollIndicator={true}
                        />
                      </motion.div>
                    </Stack>

                    {/* Column 2: Combined Power/Schedule Button */}
                    <Stack direction="column" spacing={3} alignItems="center"> {/* Increased vertical spacing */}
                      {/* ScrollablePowerButton - combines Engine and Schedule */}
                      <motion.div
                        animate={bouncingButton === 'powerSchedule' ? {
                          y: [0, -10, 0, -8, 0, -6, 0],
                          scale: [1, 1.08, 1, 1.05, 1, 1.03, 1],
                          rotate: [0, 2, 0, -2, 0, 1, 0]
                        } : {}}
                        transition={{
                          duration: 0.8,
                          repeat: bouncingButton === 'powerSchedule' ? Infinity : 0,
                          ease: "easeInOut"
                        }}
                      >
                        <ScrollablePowerButton
                          onPowerAction={(event) => {
                            // Capture button position for bird animation - use the button element
                            const buttonElement = event.target.closest('[role="button"]') || event.currentTarget;
                            if (buttonElement) {
                              const rect = buttonElement.getBoundingClientRect();
                              setButtonPosition({
                                x: rect.left + rect.width / 2,
                                y: rect.top + rect.height / 2
                              });
                            }

                            // Start bouncing animation
                            setBouncingButton('powerSchedule');
                            setTimeout(() => setBouncingButton(null), 3000);

                            // Execute the engine control logic directly
                            if (isEngineOn) {
                              setMessage(t("home.turn_off"));
                              handleDeviceControl("untar");
                              setIsEngineOn(false);
                            } else {
                              setMessage(t("home.turn_on"));
                              handleDeviceControl("as");
                              setIsEngineOn(true);
                            }
                          }}
                          onScheduleClick={(event) => {
                            // Capture button position for bird animation - use the button element
                            const buttonElement = event.target.closest('[role="button"]') || event.currentTarget;
                            if (buttonElement) {
                              const rect = buttonElement.getBoundingClientRect();
                              setButtonPosition({
                                x: rect.left + rect.width / 2,
                                y: rect.top + rect.height / 2
                              });
                            }

                            // Start bouncing animation
                            setBouncingButton('powerSchedule');
                            setTimeout(() => setBouncingButton(null), 3000);

                            // Execute the schedule function
                            handleScheduleClick();
                          }}
                          isEngineOn={isEngineOn}
                          powerLabel={isEngineOn ? t("home.engine") : t("home.start")}
                          scheduleLabel={t("home.schedule")}
                          powerIcon="⏻"
                          scheduleIcon="material-symbols:alarm"
                          colorScheme={{
                            power: buttonColors.power,
                            schedule: buttonColors.schedule
                          }}
                          size={95} // Slightly reduced for better mobile layout
                          disabled={loading}
                          showScrollIndicator={true}
                        />
                      </motion.div>
                    </Stack>

                    {/* Column 3: Combined Map/History Button */}
                    <Stack direction="column" spacing={3} alignItems="center"> {/* Increased vertical spacing */}
                      {/* ScrollableMapButton - combines Location and GPS History */}
                      <motion.div
                        animate={bouncingButton === 'mapHistory' ? {
                          y: [0, -8, 0, -6, 0, -4, 0],
                          scale: [1, 1.05, 1, 1.03, 1, 1.02, 1],
                          rotate: [0, 1, 0, -1, 0]
                        } : {}}
                        transition={{
                          duration: 0.6,
                          repeat: bouncingButton === 'mapHistory' ? Infinity : 0,
                          ease: "easeInOut"
                        }}
                      >
                        <ScrollableMapButton
                          onMapClick={(event) => {
                            // Capture button position for bird animation - use the button element
                            const buttonElement = event.target.closest('[role="button"]') || event.currentTarget;
                            if (buttonElement) {
                              const rect = buttonElement.getBoundingClientRect();
                              setButtonPosition({
                                x: rect.left + rect.width / 2,
                                y: rect.top + rect.height / 2
                              });
                            }

                            // Start bouncing animation
                            setBouncingButton('mapHistory');
                            setTimeout(() => setBouncingButton(null), 3000);

                            // Execute the location toggle logic directly
                            if (viewMap) {
                              // Hide location
                              setViewMap(false);
                            } else {
                              // Show location - get current status first
                              handleLocation(true);
                            }
                          }}
                          onHistoryClick={(event) => {
                            // Capture button position for bird animation - use the button element
                            const buttonElement = event.target.closest('[role="button"]') || event.currentTarget;
                            if (buttonElement) {
                              const rect = buttonElement.getBoundingClientRect();
                              setButtonPosition({
                                x: rect.left + rect.width / 2,
                                y: rect.top + rect.height / 2
                              });
                            }

                            // Start bouncing animation
                            setBouncingButton('mapHistory');
                            setTimeout(() => setBouncingButton(null), 3000);

                            // Execute the GPS history function
                            handleGpsHistoryClick();
                          }}
                          mapLabel={viewMap ? t("home.hide_location") : t("home.show_location")}
                          historyLabel={t("home.history")}
                          mapIcon={viewMap ? "material-symbols:location-off" : "material-symbols:location-on"}
                          historyIcon="material-symbols:history"
                          colorScheme={{
                            location: buttonColors.location,
                            history: buttonColors.history
                          }}
                          size={85} // Slightly reduced for better mobile layout
                          disabled={loading}
                          showScrollIndicator={true}
                        />
                      </motion.div>
                    </Stack>
                  </Stack>
                </Box>
              )}

            </Stack>
          </Grid>

          <Grid item xs={12}>
            {viewMap &&
              deviceStatus?.Lat &&
              deviceStatus?.Lon &&
              deviceStatus?.Lat !== 0 &&
              deviceStatus?.Lon !==0 && (
                <CarLocation lat={deviceStatus?.Lat} lng={deviceStatus?.Lon} />
              )}
          </Grid>
        </Grid>
      </Container>

      {/* Bird Animation - Handled by FindingScreen when loading */}

      {/* Pincode Setup Dialog */}
      <PinCodeSetupDialog
        open={showPincodeSetup}
        onClose={() => setShowPincodeSetup(false)}
        deviceNumber={user?.device?.deviceNumber}
      />

      <PinCodeConfirm
        open={!checkedPincode}
        onModalClose={() => {
          setCheckedPincode(true);
        }}
        onSuccess={confirmed}
      />

      {/* Scheduler Dialog */}
      <SchedulerDialog
        open={schedulerDialogOpen}
        onClose={handleSchedulerClose}
        deviceNumber={user?.device?.deviceNumber}
        deviceType={user?.device?.type}
      />
    </Page>
  );
}
