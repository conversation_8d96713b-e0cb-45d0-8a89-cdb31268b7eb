import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Paper
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import Page from '../../components/Page';
import Layout from '../../layout';
import axios from '../../utils/axios';

// Import chart components
import CommandOverviewChart from '../../components/admin/statistics/CommandOverviewChart';
import ResponseTimeChart from '../../components/admin/statistics/ResponseTimeChart';
import TopUsersTable from '../../components/admin/statistics/TopUsersTable';
import DevicePerformanceChart from '../../components/admin/statistics/DevicePerformanceChart';
import TimeBasedChart from '../../components/admin/statistics/TimeBasedChart';
import StatisticsFilters from '../../components/admin/statistics/StatisticsFilters';
import ExportButton from '../../components/admin/statistics/ExportButton';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`statistics-tabpanel-${index}`}
      aria-labelledby={`statistics-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function StatisticsDashboard() {
  const { t } = useTranslation();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Filter states
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    endDate: new Date(),
    period: 'daily',
    deviceNumber: '',
    userId: '',
    commandType: ''
  });

  // Data states
  const [commandStats, setCommandStats] = useState(null);
  const [topUsers, setTopUsers] = useState([]);
  const [deviceAnalytics, setDeviceAnalytics] = useState(null);
  const [timeBasedStats, setTimeBasedStats] = useState([]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  // Fetch command statistics
  const fetchCommandStatistics = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        period: filters.period,
        ...(filters.deviceNumber && { deviceNumber: filters.deviceNumber }),
        ...(filters.userId && { userId: filters.userId }),
        ...(filters.commandType && { commandType: filters.commandType })
      };

      const response = await axios.get('/api/admin/statistics/commands', { params });
      if (response.data.success) {
        setCommandStats(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching command statistics:', err);
      setError('Failed to fetch command statistics');
    }
  };

  // Fetch top users
  const fetchTopUsers = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 10
      };

      const response = await axios.get('/api/admin/statistics/top-users', { params });
      if (response.data.success) {
        setTopUsers(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching top users:', err);
      setError('Failed to fetch top users');
    }
  };

  // Fetch device analytics
  const fetchDeviceAnalytics = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 20
      };

      const response = await axios.get('/api/admin/statistics/device-analytics', { params });
      if (response.data.success) {
        setDeviceAnalytics(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching device analytics:', err);
      setError('Failed to fetch device analytics');
    }
  };

  // Fetch time-based statistics
  const fetchTimeBasedStatistics = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        groupBy: 'day',
        period: filters.period
      };

      const response = await axios.get('/api/admin/statistics/time-based', { params });
      if (response.data.success) {
        setTimeBasedStats(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching time-based statistics:', err);
      setError('Failed to fetch time-based statistics');
    }
  };

  // Load all data
  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        fetchCommandStatistics(),
        fetchTopUsers(),
        fetchDeviceAnalytics(),
        fetchTimeBasedStatistics()
      ]);
    } catch (err) {
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [filters]);

  if (loading && !commandStats) {
    return (
      <Page title="Statistics Dashboard">
        <Layout />
        <Container sx={{ py: { xs: 12 } }} maxWidth="xl">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Page>
    );
  }

  return (
    <Page title="Statistics Dashboard">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth="xl">
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4">
            Usage Statistics Dashboard
          </Typography>
          <ExportButton 
            filters={filters}
            onExport={() => {/* Export functionality */}}
          />
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <StatisticsFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onRefresh={loadData}
          loading={loading}
        />

        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="Overview" />
            <Tab label="Command Analytics" />
            <Tab label="Device Performance" />
            <Tab label="User Activity" />
            <Tab label="Time Analysis" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <CommandOverviewChart data={commandStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <ResponseTimeChart data={commandStats} />
              </Grid>
              <Grid item xs={12}>
                <TopUsersTable data={topUsers} />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <CommandOverviewChart data={commandStats} detailed={true} />
              </Grid>
              <Grid item xs={12} md={6}>
                <ResponseTimeChart data={commandStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Command Type Distribution
                    </Typography>
                    {/* Command type chart will be implemented */}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <DevicePerformanceChart data={deviceAnalytics} />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TopUsersTable data={topUsers} detailed={true} />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TimeBasedChart data={timeBasedStats} />
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>
      </Container>
    </Page>
  );
}
