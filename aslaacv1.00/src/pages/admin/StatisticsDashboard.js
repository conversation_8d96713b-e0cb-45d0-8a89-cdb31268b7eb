import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Paper,
  Chip
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import Page from '../../components/Page';
import Layout from '../../layout';
import axios from '../../utils/axios';
import useAuth from '../../hooks/useAuth';
import statisticsMqttService from '../../services/statisticsMqttService';

// Import chart components
import CommandOverviewChart from '../../components/admin/statistics/CommandOverviewChart';
import ResponseTimeChart from '../../components/admin/statistics/ResponseTimeChart';
import TopUsersTable from '../../components/admin/statistics/TopUsersTable';
import DevicePerformanceChart from '../../components/admin/statistics/DevicePerformanceChart';
import TimeBasedChart from '../../components/admin/statistics/TimeBasedChart';
import StatisticsFilters from '../../components/admin/statistics/StatisticsFilters';
import ExportButton from '../../components/admin/statistics/ExportButton';
import DetailedCommandLogs from '../../components/admin/statistics/DetailedCommandLogs';
import DeviceDetailsDialog from '../../components/admin/statistics/DeviceDetailsDialog';
import UserDetailsDialog from '../../components/admin/statistics/UserDetailsDialog';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`statistics-tabpanel-${index}`}
      aria-labelledby={`statistics-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function StatisticsDashboard() {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [realtimeConnected, setRealtimeConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    endDate: new Date(),
    period: 'daily',
    deviceNumber: '',
    userId: '',
    commandType: ''
  });

  // Data states
  const [commandStats, setCommandStats] = useState(null);
  const [topUsers, setTopUsers] = useState([]);
  const [deviceAnalytics, setDeviceAnalytics] = useState(null);
  const [timeBasedStats, setTimeBasedStats] = useState([]);

  // Real-time update counters
  const [realtimeStats, setRealtimeStats] = useState({
    newCommands: 0,
    activeDevices: 0,
    activeUsers: 0
  });

  // Drill-down dialog states
  const [showDetailedLogs, setShowDetailedLogs] = useState(false);
  const [showDeviceDetails, setShowDeviceDetails] = useState(false);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [selectedUser, setSelectedUser] = useState({ id: null, info: null });

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    // Update MQTT subscription with new filters
    if (realtimeConnected) {
      statisticsMqttService.publishFilters(newFilters);
    }
  };

  // Real-time MQTT event handlers
  const handleRealtimeUpdate = useCallback((data) => {
    console.log('Real-time statistics update:', data);
    setLastUpdate(new Date());

    if (data.type === 'realtime_stats') {
      setRealtimeStats(prev => ({
        ...prev,
        ...data.data
      }));
    }
  }, []);

  const handleCommandStatistics = useCallback((data) => {
    console.log('Command statistics update:', data);
    // Incrementally update command stats if they match current filters
    if (data.type === 'command_update') {
      setRealtimeStats(prev => ({
        ...prev,
        newCommands: prev.newCommands + 1
      }));

      // Optionally refresh full stats periodically
      if (realtimeStats.newCommands % 10 === 0) {
        fetchCommandStatistics();
      }
    }
  }, [realtimeStats.newCommands]);

  const handleDeviceStatistics = useCallback((data) => {
    console.log('Device statistics update:', data);
    if (data.type === 'device_update') {
      // Update device analytics incrementally
      fetchDeviceAnalytics();
    }
  }, []);

  const handleUserStatistics = useCallback((data) => {
    console.log('User statistics update:', data);
    if (data.type === 'user_update') {
      // Update user stats incrementally
      fetchTopUsers();
    }
  }, []);

  const handleMqttConnected = useCallback(() => {
    console.log('Statistics MQTT connected');
    setRealtimeConnected(true);
    setError(null);

    // Subscribe to statistics updates with current filters
    statisticsMqttService.subscribeToStatistics(filters);
  }, [filters]);

  const handleMqttDisconnected = useCallback(() => {
    console.log('Statistics MQTT disconnected');
    setRealtimeConnected(false);
  }, []);

  const handleMqttError = useCallback((error) => {
    console.error('Statistics MQTT error:', error);
    setError('Real-time connection error: ' + error.message);
  }, []);

  // Drill-down handlers
  const handleUserClick = useCallback((userId, userInfo) => {
    setSelectedUser({ id: userId, info: userInfo });
    setShowUserDetails(true);
  }, []);

  const handleDeviceClick = useCallback((deviceNumber) => {
    setSelectedDevice(deviceNumber);
    setShowDeviceDetails(true);
  }, []);

  const handleShowDetailedLogs = useCallback(() => {
    setShowDetailedLogs(true);
  }, []);

  // Fetch command statistics
  const fetchCommandStatistics = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        period: filters.period,
        ...(filters.deviceNumber && { deviceNumber: filters.deviceNumber }),
        ...(filters.userId && { userId: filters.userId }),
        ...(filters.commandType && { commandType: filters.commandType })
      };

      const response = await axios.get('/api/admin/statistics/commands', { params });
      if (response.data.success) {
        setCommandStats(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching command statistics:', err);
      setError('Failed to fetch command statistics');
    }
  };

  // Fetch top users
  const fetchTopUsers = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 10
      };

      const response = await axios.get('/api/admin/statistics/top-users', { params });
      if (response.data.success) {
        setTopUsers(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching top users:', err);
      setError('Failed to fetch top users');
    }
  };

  // Fetch device analytics
  const fetchDeviceAnalytics = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 20
      };

      const response = await axios.get('/api/admin/statistics/device-analytics', { params });
      if (response.data.success) {
        setDeviceAnalytics(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching device analytics:', err);
      setError('Failed to fetch device analytics');
    }
  };

  // Fetch time-based statistics
  const fetchTimeBasedStatistics = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        groupBy: 'day',
        period: filters.period
      };

      const response = await axios.get('/api/admin/statistics/time-based', { params });
      if (response.data.success) {
        setTimeBasedStats(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching time-based statistics:', err);
      setError('Failed to fetch time-based statistics');
    }
  };

  // Load all data
  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        fetchCommandStatistics(),
        fetchTopUsers(),
        fetchDeviceAnalytics(),
        fetchTimeBasedStatistics()
      ]);
    } catch (err) {
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Setup MQTT real-time connection
  useEffect(() => {
    if (user) {
      // Connect to MQTT for real-time updates
      statisticsMqttService.connect(user);

      // Set up event listeners
      statisticsMqttService.on('connected', handleMqttConnected);
      statisticsMqttService.on('disconnected', handleMqttDisconnected);
      statisticsMqttService.on('error', handleMqttError);
      statisticsMqttService.on('realtime-update', handleRealtimeUpdate);
      statisticsMqttService.on('command-statistics', handleCommandStatistics);
      statisticsMqttService.on('device-statistics', handleDeviceStatistics);
      statisticsMqttService.on('user-statistics', handleUserStatistics);

      // Cleanup function
      return () => {
        statisticsMqttService.off('connected', handleMqttConnected);
        statisticsMqttService.off('disconnected', handleMqttDisconnected);
        statisticsMqttService.off('error', handleMqttError);
        statisticsMqttService.off('realtime-update', handleRealtimeUpdate);
        statisticsMqttService.off('command-statistics', handleCommandStatistics);
        statisticsMqttService.off('device-statistics', handleDeviceStatistics);
        statisticsMqttService.off('user-statistics', handleUserStatistics);
        statisticsMqttService.disconnect();
      };
    }
  }, [user, handleMqttConnected, handleMqttDisconnected, handleMqttError,
      handleRealtimeUpdate, handleCommandStatistics, handleDeviceStatistics, handleUserStatistics]);

  useEffect(() => {
    loadData();
  }, [filters]);

  if (loading && !commandStats) {
    return (
      <Page title="Statistics Dashboard">
        <Layout />
        <Container sx={{ py: { xs: 12 } }} maxWidth="xl">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Page>
    );
  }

  return (
    <Page title="Statistics Dashboard">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth="xl">
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4">
              Usage Statistics Dashboard
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 1 }}>
              <Chip
                label={realtimeConnected ? 'Real-time Connected' : 'Real-time Disconnected'}
                color={realtimeConnected ? 'success' : 'error'}
                size="small"
                variant="outlined"
              />
              {lastUpdate && (
                <Typography variant="body2" color="text.secondary">
                  Last update: {lastUpdate.toLocaleTimeString()}
                </Typography>
              )}
              {realtimeConnected && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip
                    label={`${realtimeStats.newCommands} new commands`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                  <Chip
                    label={`${realtimeStats.activeDevices} active devices`}
                    size="small"
                    color="info"
                    variant="outlined"
                  />
                  <Chip
                    label={`${realtimeStats.activeUsers} active users`}
                    size="small"
                    color="secondary"
                    variant="outlined"
                  />
                </Box>
              )}
            </Box>
          </Box>
          <ExportButton
            filters={filters}
            onExport={() => {/* Export functionality */}}
          />
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <StatisticsFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onRefresh={loadData}
          loading={loading}
        />

        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="Overview" />
            <Tab label="Command Analytics" />
            <Tab label="Device Performance" />
            <Tab label="User Activity" />
            <Tab label="Time Analysis" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <CommandOverviewChart data={commandStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <ResponseTimeChart data={commandStats} />
              </Grid>
              <Grid item xs={12}>
                <TopUsersTable
                  data={topUsers}
                  onUserClick={handleUserClick}
                  filters={filters}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <CommandOverviewChart data={commandStats} detailed={true} />
              </Grid>
              <Grid item xs={12} md={6}>
                <ResponseTimeChart data={commandStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Command Type Distribution
                    </Typography>
                    {/* Command type chart will be implemented */}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <DevicePerformanceChart
                  data={deviceAnalytics}
                  onDeviceClick={handleDeviceClick}
                  filters={filters}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TopUsersTable
                  data={topUsers}
                  detailed={true}
                  onUserClick={handleUserClick}
                  filters={filters}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TimeBasedChart data={timeBasedStats} />
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>

        {/* Drill-down Dialogs */}
        <DetailedCommandLogs
          open={showDetailedLogs}
          onClose={() => setShowDetailedLogs(false)}
          filters={filters}
          title="Detailed Command Logs"
        />

        <DeviceDetailsDialog
          open={showDeviceDetails}
          onClose={() => setShowDeviceDetails(false)}
          deviceNumber={selectedDevice}
          filters={filters}
        />

        <UserDetailsDialog
          open={showUserDetails}
          onClose={() => setShowUserDetails(false)}
          userId={selectedUser.id}
          userInfo={selectedUser.info}
          filters={filters}
        />
      </Container>
    </Page>
  );
}
