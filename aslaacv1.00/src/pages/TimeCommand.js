import { <PERSON>, Container, <PERSON>rid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Text<PERSON>ield, Typography, CircularProgress, But<PERSON> } from "@mui/material";
// TimePicker replaced with regular time input
import dayjs from 'dayjs';
import { useState } from "react";
import CarFront from "../components/CarFront";
import ChipIcon from "../components/ChipIconi";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectCoverflow, Navigation } from 'swiper';
import Iconify from '../components/Iconify';
import { IconButtonAnimate } from "../components/animate";

import useAuth from "../hooks/useAuth";
import Page from "../components/Page";
import Layout from "../layout";


import axios from '../utils/axios';
import { useSnackbar } from "notistack";
// LoadingButton replaced with regular Button + CircularProgress
import { Icon } from "@iconify/react";
import { maskPhoneNumber } from '../utils/phoneUtils';

// ----------------------------------------------------------------------
const SLIDER_SETTING = {
    effect: "coverflow",
    grabCursor: true,
    centeredSlides: true,
    loop: true,
    pagination: false,
    slidesPerView: 2,
    spaceBetween: 60,
    coverflowEffect: {
        rotate: 0,
        stretch: 0,
        depth: 180,
        modifier: 3,
        slideShadows: true,
    },
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },
}

export default function TimeCommand() {
    const { user } = useAuth();
    const [deviceId, setDeviceId] = useState();
    const [nextTime, setNextTime] = useState(0);
    const [selectedDevice, setSelectedDevice] = useState(null);
    const { enqueueSnackbar } = useSnackbar();
    const [minTemp, setMinTemperature] = useState(0);
    const [maxTemp, setMaxTemperature] = useState(0);
    const [command, setCommand] = useState(':turnon');
    const [logs, setLogs] = useState([]);
    const [loading, setLoading] = useState(false);
    const onIndexChanged = (swiper) => {
        let index = swiper.realIndex;
        if (index >= user?.devices?.length) {
            index = 0;
        }
        setDeviceId(user?.devices[index].deviceNumber);
        setSelectedDevice(user?.devices[index]);
    }
    const sendTimeCommand = () => {
        const time = new Date(value);
        const time1 = `${time.getHours()}.${time.getMinutes()}`;
        setLoading(true)
        let payload = `${time.getHours()}.${time.getMinutes()}.${nextTime}`;

        let request = { deviceNumber: deviceId, time1, time2: nextTime, cmd: command };

        if(command === ':temp'){
            request = { deviceNumber: deviceId, minTemp, maxTemp, cmd: command };
            payload = `${minTemp}.${maxTemp}`;
        }
        axios
            .post(`/api/device/control/${command}`, request)
            .then((res) => {

                setLoading(false);

                if (res.data.success) {
                    // setAction(preStatus || command);
                    // setDescription(`${res.data.action} was successful sent, please wait a moment.`);
                    const l = logs.slice(0, logs.length);
                    l.push({ deviceNumber: deviceId, command, result: 'success', payload })
                    setLogs(l);
                } else if (res.data.err) {
                    // setAction('');
                    if (typeof res.data.err === 'object') {
                        // setDescription(
                        //     `code = ${res.data.err.code}, host= ${res.data.err.address}, port =  ${res.data.err.port}`
                        // );
                        const l = logs.slice(0, logs.length);
                        l.push({ deviceNumber: deviceId, command, result: 'failed', payload })
                        setLogs(l);
                    }

                    if (typeof res.data.err === 'string') {
                        // setDescription(res.data.err);


                        const l = logs.slice(0, logs.length);
                        l.push({ deviceNumber: deviceId, command, result: 'failed', payload })
                        enqueueSnackbar(res.data.err, { variant: 'error' });
                        setLogs(l);
                    }
                }
                setTimeout(() => {
                    // setLoading(false);
                }, 3000);

            })
            .catch(() => {
                // console.log(res)
                const l = logs.slice(0, logs.length);
                l.push({ deviceNumber: deviceId, command, result: 'failed', payload })
                setLogs(l)
                enqueueSnackbar("Please check your connection or status", { variant: "error" });
                setLoading(false);
            });
    }
    const [value, setValue] = useState(dayjs(new Date()));

    const handleChange = (newValue) => {

        setValue(newValue);
    };
    return (
        <Page title="Device registration">
            <Layout />
            <Container sx={{ py: { xs: 12 } }} maxWidth={'sm'}>
                <Stack>
                    <Grid container>
                        <Grid item xs={12} textAlign={"center"}>
                            <Box sx={{ position: 'relative', marginBottom: 2 }}>
                                <Swiper {...SLIDER_SETTING} modules={[Navigation, EffectCoverflow]} onActiveIndexChange={onIndexChanged}>
                                    {user?.devices?.map((device, index) => (
                                        <SwiperSlide key={index}>

                                            <Box>
                                                <Typography variant='h6' sx={{ pt: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                                    <Iconify icon={'carbon:sim-card'} width={24} height={24}></Iconify>&nbsp;{maskPhoneNumber(device?.phoneNumber) || ' not available'}
                                                </Typography>
                                                {device?.uix.includes('Car') && <CarFront disabledLink />}
                                                {device?.uix === 'Chip' && <Box sx={{ marginX: -2 }}><ChipIcon sx={{ color: 'yellow' }} /></Box>}
                                                <Typography variant='subtitle2' sx={{ pt: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }} color={"grey.500"}>
                                                    <Iconify icon={device?.isDefault ? "fe:check-verified" : "codicon:unverified"}></Iconify>{device?.deviceNumber}
                                                </Typography>
                                                <Box sx={{ position: 'absolute', top: '30%', right: '0%', }}>
                                                    <Iconify color={device?.type === 'sms' ? 'grey.500' : 'red'} icon={device?.type === 'sms' ? 'arcticons:sms-gate' : 'healthicons:network-4g-outline'} width={24} height={24} />
                                                </Box>
                                            </Box>

                                        </SwiperSlide>
                                    ))}


                                </Swiper>
                                <Stack direction='row' pt={{ xs: 1, md: 2 }} justifyContent='center' >
                                    <IconButtonAnimate className="swiper-button-prev">
                                        <Iconify icon='eva:arrow-back-outline' width={30} height={30} />
                                    </IconButtonAnimate>
                                    <IconButtonAnimate className="swiper-button-next">

                                        <Iconify icon='eva:arrow-forward-outline' width={30} height={30} />
                                    </IconButtonAnimate>
                                </Stack>
                            </Box>

                        </Grid>
                    </Grid>
                </Stack>
                <Stack gap={2} direction={{ sm: 'row', xs: 'column' }} mb={2} >
                    {
                        selectedDevice && selectedDevice?.uix?.includes('Car') &&
                        <TextField select label={'Choose Command'} sx={{ minWidth: 160, flexGrow: 1 }} value={command} onChange={e => setCommand(e.target.value)}>
                            <MenuItem value={':turnon'}>Start</MenuItem>
                            <MenuItem value={':turnoff'}>Stop</MenuItem>
                            <MenuItem value={':lock'}>Lock</MenuItem>
                            <MenuItem value={':unlock'}>Unlock</MenuItem>
                            {selectedDevice && (selectedDevice?.uix?.includes('CarV1.2') || selectedDevice?.uix?.includes('Car2.2')) &&
                                <MenuItem value={':temp'}>Temperature</MenuItem>
                            }
                        </TextField>
                    }
                    {
                        selectedDevice && selectedDevice?.uix?.includes('Chip') &&
                        <TextField select label={'Choose Command'} sx={{ minWidth: 160, flexGrow: 1 }} value={command} onChange={e => setCommand(e.target.value)}>
                            <MenuItem value={':on1'}>On1</MenuItem>
                            <MenuItem value={':on2'}>On2</MenuItem>
                            <MenuItem value={':off1'}>Off1</MenuItem>
                            <MenuItem value={':off2'}>Off2</MenuItem>
                        </TextField>
                    }
                    {command && command === ':temp' &&
                        <Stack gap={2} >
                            <TextField InputProps={{ inputProps: { min: -40, max: 100, } }}
                                label='Temperature Min Value' type='number' value={minTemp} onChange={(e) => { setMinTemperature(e.target.value) }} ></TextField>
                            <TextField InputProps={{ inputProps: { min: -40, max: 100, } }}
                                label='Temperature Max Value' type='number' value={maxTemp} onChange={(e) => { setMaxTemperature(e.target.value) }} ></TextField>
                        </Stack>

                    }
                    {command && command !== ':temp' &&
                        <TextField
                            label="Time"
                            type="time"
                            value={value ? value.format('HH:mm') : ''}
                            onChange={(e) => handleChange(dayjs(`2000-01-01T${e.target.value}`))}
                            sx={{ flexGrow: 1 }}
                            InputLabelProps={{
                                shrink: true,
                            }}
                        />
                    }
                    {
                        selectedDevice && selectedDevice?.type === 'sms' &&
                        <TextField label={'During'} type="number" value={nextTime} onChange={(e) => setNextTime(e.target.value)} />
                    }
                    <Button
                        disabled={loading}
                        sx={{ border: '1px solid', borderColor: 'grey.50048', flexGrow: 1 }}
                        size="large"
                        onClick={sendTimeCommand}
                        startIcon={loading ? <CircularProgress size={20} /> : null}
                    >
                        Send
                    </Button>
                </Stack>
                <Stack gap={2} >
                    <Grid container>
                        <Grid item xs={5}>
                            Device Number
                        </Grid>
                        <Grid item xs={3}>
                            Command
                        </Grid>
                        <Grid item xs={3}>
                            Payload
                        </Grid>
                        <Grid item xs={1}>
                            Res
                        </Grid>
                    </Grid>
                    {logs?.reverse()?.map((log, index) => (
                        <Grid container key={index}>
                            <Grid item xs={5}>
                                <Typography sx={{ width: '100%', overflow: 'hidden' }}>
                                    {log.deviceNumber}
                                </Typography>



                            </Grid>
                            <Grid item xs={3} sx={{ textAlign: 'center' }}>
                                {log.command}
                            </Grid>
                            <Grid item xs={3}>
                                {log.payload}
                            </Grid>
                            <Grid item xs={1}>
                                {log.result ? <Icon icon="mdi:success-circle-outline" color="cyan" /> : <Icon icon="uil:times-circle" color="red" />}
                            </Grid>
                        </Grid>
                    ))}

                </Stack>
            </Container>
        </Page >
    )
}