import io from 'socket.io-client';
import { HOST_API } from '../config';

class StatisticsSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  // Connect to the statistics socket
  connect(user) {
    if (this.socket && this.isConnected) {
      return;
    }

    try {
      this.socket = io.connect(`${HOST_API}`, {
        transports: ["polling", "websocket"],
        query: {
          type: 'statistics',
          userId: user.id
        }
      });

      this.setupEventHandlers(user);
    } catch (error) {
      console.error('Statistics Socket: Connection error', error);
    }
  }

  // Setup socket event handlers
  setupEventHandlers(user) {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Statistics Socket: Connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Emit login event for statistics
      this.socket.emit('statistics-login', {
        ...user,
        type: 'statistics'
      });

      // Notify listeners about connection
      this.emit('connected');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Statistics Socket: Disconnected', reason);
      this.isConnected = false;
      this.emit('disconnected', reason);

      // Auto-reconnect logic
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.scheduleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Statistics Socket: Connection error', error);
      this.isConnected = false;
      this.scheduleReconnect();
    });

    // Listen for real-time statistics updates
    this.socket.on('statistics-update', (data) => {
      console.log('Statistics Socket: Received update', data);
      this.emit('statistics-update', data);
    });

    // Listen for new command events
    this.socket.on('new-command', (data) => {
      console.log('Statistics Socket: New command', data);
      this.emit('new-command', data);
    });

    // Listen for command response events
    this.socket.on('command-response', (data) => {
      console.log('Statistics Socket: Command response', data);
      this.emit('command-response', data);
    });

    // Listen for device status updates
    this.socket.on('device-status-update', (data) => {
      console.log('Statistics Socket: Device status update', data);
      this.emit('device-status-update', data);
    });

    // Listen for user activity updates
    this.socket.on('user-activity-update', (data) => {
      console.log('Statistics Socket: User activity update', data);
      this.emit('user-activity-update', data);
    });
  }

  // Schedule reconnection
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Statistics Socket: Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Statistics Socket: Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      if (!this.isConnected && this.socket) {
        this.socket.connect();
      }
    }, delay);
  }

  // Subscribe to statistics updates for specific filters
  subscribeToStatistics(filters) {
    if (!this.socket || !this.isConnected) {
      console.warn('Statistics Socket: Not connected, cannot subscribe');
      return;
    }

    this.socket.emit('subscribe-statistics', filters);
  }

  // Unsubscribe from statistics updates
  unsubscribeFromStatistics() {
    if (!this.socket || !this.isConnected) {
      return;
    }

    this.socket.emit('unsubscribe-statistics');
  }

  // Request real-time statistics refresh
  requestStatisticsRefresh(filters) {
    if (!this.socket || !this.isConnected) {
      console.warn('Statistics Socket: Not connected, cannot request refresh');
      return;
    }

    this.socket.emit('request-statistics-refresh', filters);
  }

  // Add event listener
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove event listener
  off(event, callback) {
    if (!this.listeners.has(event)) {
      return;
    }

    const listeners = this.listeners.get(event);
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  // Emit event to listeners
  emit(event, data) {
    if (!this.listeners.has(event)) {
      return;
    }

    this.listeners.get(event).forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Statistics Socket: Error in event listener for ${event}`, error);
      }
    });
  }

  // Disconnect from socket
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.listeners.clear();
    this.reconnectAttempts = 0;
  }

  // Get connection status
  get connected() {
    return this.isConnected;
  }
}

// Create singleton instance
const statisticsSocketService = new StatisticsSocketService();

export default statisticsSocketService;
