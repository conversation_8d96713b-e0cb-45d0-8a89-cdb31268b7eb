import mqttService from './mqttService';

class StatisticsMqttService {
  constructor() {
    this.isConnected = false;
    this.listeners = new Map();
    this.subscribedTopics = new Set();
    this.statisticsTopics = {
      commands: 'statistics/commands',
      devices: 'statistics/devices',
      users: 'statistics/users',
      realtime: 'statistics/realtime'
    };
  }

  // Connect to MQTT for statistics
  connect(user) {
    if (this.isConnected) {
      return;
    }

    try {
      // Use existing MQTT service
      if (mqttService.isConnected) {
        this.setupMqttHandlers();
        this.isConnected = true;
        this.emit('connected');
      } else {
        // Listen for MQTT connection
        mqttService.on('connect', () => {
          this.setupMqttHandlers();
          this.isConnected = true;
          this.emit('connected');
        });
      }
    } catch (error) {
      console.error('Statistics MQTT: Connection error', error);
    }
  }

  // Setup MQTT message handlers
  setupMqttHandlers() {
    // Set up message handler for statistics topics
    mqttService.on('message', (topic, message) => {
      try {
        // Only process messages for statistics topics
        if (topic.startsWith('statistics/')) {
          const data = JSON.parse(message.toString());
          console.log(`Statistics MQTT: Received message on ${topic}`, data);

          // Emit specific events based on topic
          if (topic === this.statisticsTopics.commands) {
            this.emit('command-statistics', data);
          } else if (topic === this.statisticsTopics.devices) {
            this.emit('device-statistics', data);
          } else if (topic === this.statisticsTopics.users) {
            this.emit('user-statistics', data);
          } else if (topic === this.statisticsTopics.realtime) {
            this.emit('realtime-update', data);
          }

          // Also emit a general statistics update event
          this.emit('statistics-update', { topic, data });
        }
      } catch (error) {
        console.error(`Statistics MQTT: Error processing message on ${topic}`, error);
      }
    });

    // Handle MQTT connection events
    mqttService.on('connect', () => {
      console.log('Statistics MQTT: Connected');
      this.isConnected = true;

      // Resubscribe to topics if needed
      this.subscribedTopics.forEach(topic => {
        mqttService.subscribe(topic);
      });

      this.emit('connected');
    });

    mqttService.on('disconnect', () => {
      console.log('Statistics MQTT: Disconnected');
      this.isConnected = false;
      this.emit('disconnected');
    });

    mqttService.on('error', (error) => {
      console.error('Statistics MQTT: Error', error);
      this.emit('error', error);
    });
  }

  // Subscribe to statistics updates for specific filters
  subscribeToStatistics(filters = {}) {
    if (!mqttService.isConnected) {
      console.warn('Statistics MQTT: Not connected, cannot subscribe');
      return;
    }

    // Subscribe to all statistics topics
    Object.values(this.statisticsTopics).forEach(topic => {
      if (!this.subscribedTopics.has(topic)) {
        mqttService.subscribe(topic);
        this.subscribedTopics.add(topic);
        console.log(`Statistics MQTT: Subscribed to ${topic}`);
      }
    });

    // If filters are provided, publish them to request filtered updates
    if (Object.keys(filters).length > 0) {
      this.publishFilters(filters);
    }
  }

  // Unsubscribe from statistics updates
  unsubscribeFromStatistics() {
    if (!mqttService.isConnected) {
      return;
    }

    // Unsubscribe from all statistics topics
    this.subscribedTopics.forEach(topic => {
      mqttService.unsubscribe(topic);
      console.log(`Statistics MQTT: Unsubscribed from ${topic}`);
    });

    this.subscribedTopics.clear();
  }

  // Publish filter preferences to the server
  publishFilters(filters) {
    if (!mqttService.isConnected) {
      console.warn('Statistics MQTT: Not connected, cannot publish filters');
      return;
    }

    const filterMessage = {
      type: 'filter_update',
      filters,
      timestamp: Date.now()
    };

    mqttService.publish('statistics/filters', JSON.stringify(filterMessage));
    console.log('Statistics MQTT: Published filters', filters);
  }

  // Request real-time statistics refresh
  requestStatisticsRefresh(filters = {}) {
    if (!mqttService.isConnected) {
      console.warn('Statistics MQTT: Not connected, cannot request refresh');
      return;
    }

    const refreshMessage = {
      type: 'refresh_request',
      filters,
      timestamp: Date.now()
    };

    mqttService.publish('statistics/refresh', JSON.stringify(refreshMessage));
    console.log('Statistics MQTT: Requested refresh with filters', filters);
  }

  // Add event listener
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove event listener
  off(event, callback) {
    if (!this.listeners.has(event)) {
      return;
    }

    const listeners = this.listeners.get(event);
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  // Emit event to listeners
  emit(event, data) {
    if (!this.listeners.has(event)) {
      return;
    }

    this.listeners.get(event).forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Statistics Socket: Error in event listener for ${event}`, error);
      }
    });
  }

  // Disconnect from MQTT statistics
  disconnect() {
    this.unsubscribeFromStatistics();
    this.isConnected = false;
    this.listeners.clear();
    this.subscribedTopics.clear();
  }

  // Get connection status
  get connected() {
    return this.isConnected && mqttService.isConnected;
  }

  // Publish command statistics update (for server-side use)
  publishCommandUpdate(commandData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'command_update',
      data: commandData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.commands, JSON.stringify(message));
  }

  // Publish device statistics update (for server-side use)
  publishDeviceUpdate(deviceData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'device_update',
      data: deviceData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.devices, JSON.stringify(message));
  }

  // Publish user activity update (for server-side use)
  publishUserUpdate(userData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'user_update',
      data: userData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.users, JSON.stringify(message));
  }

  // Publish real-time statistics update
  publishRealtimeUpdate(statsData) {
    if (!mqttService.isConnected) {
      return;
    }

    const message = {
      type: 'realtime_stats',
      data: statsData,
      timestamp: Date.now()
    };

    mqttService.publish(this.statisticsTopics.realtime, JSON.stringify(message));
  }
}

// Create singleton instance
const statisticsMqttService = new StatisticsMqttService();

export default statisticsMqttService;
