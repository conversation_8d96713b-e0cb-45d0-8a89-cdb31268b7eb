import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Avatar,
  LinearProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import PersonIcon from '@mui/icons-material/Person';
import { formatDistanceToNow } from 'date-fns';

TopUsersTable.propTypes = {
  data: PropTypes.array,
  detailed: PropTypes.bool,
  onUserClick: PropTypes.func,
  filters: PropTypes.object
};

export default function TopUsersTable({ data = [], detailed = false, onUserClick, filters = {} }) {
  const theme = useTheme();

  const handleUserClick = (user) => {
    if (onUserClick) {
      onUserClick(user._id, user);
    }
  };

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Top Users by Command Count
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              No user data available
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const getSuccessRateColor = (rate) => {
    if (rate >= 90) return 'success';
    if (rate >= 70) return 'warning';
    return 'error';
  };

  const getResponseTimeColor = (time) => {
    if (time < 1000) return theme.palette.success.main;
    if (time < 3000) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Top Users by Command Count
        </Typography>
        
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell align="center">Total Commands</TableCell>
                <TableCell align="center">Success Rate</TableCell>
                <TableCell align="center">Avg Response Time</TableCell>
                {detailed && <TableCell align="center">Failed Commands</TableCell>}
                <TableCell align="center">Last Activity</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.map((user, index) => (
                <TableRow
                  key={user._id || index}
                  hover
                  onClick={() => handleUserClick(user)}
                  sx={{ cursor: onUserClick ? 'pointer' : 'default' }}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: theme.palette.primary.main, width: 32, height: 32 }}>
                        <PersonIcon fontSize="small" />
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2">
                          {user.username || 'Unknown User'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {user.phoneNumber || 'No phone'}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  
                  <TableCell align="center">
                    <Typography variant="h6" color="primary.main">
                      {user.totalCommands || 0}
                    </Typography>
                  </TableCell>
                  
                  <TableCell align="center">
                    <Box sx={{ minWidth: 100 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={user.successRate || 0}
                          sx={{ 
                            flexGrow: 1, 
                            height: 8, 
                            borderRadius: 4,
                            bgcolor: 'grey.200',
                            '& .MuiLinearProgress-bar': {
                              bgcolor: getSuccessRateColor(user.successRate || 0) === 'success' 
                                ? theme.palette.success.main 
                                : getSuccessRateColor(user.successRate || 0) === 'warning'
                                ? theme.palette.warning.main
                                : theme.palette.error.main
                            }
                          }}
                        />
                        <Typography variant="body2" sx={{ minWidth: 40 }}>
                          {Math.round(user.successRate || 0)}%
                        </Typography>
                      </Box>
                      <Chip
                        label={`${user.successfulCommands || 0}/${user.totalCommands || 0}`}
                        size="small"
                        color={getSuccessRateColor(user.successRate || 0)}
                        variant="outlined"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  </TableCell>
                  
                  <TableCell align="center">
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: getResponseTimeColor(user.avgResponseTime || 0),
                        fontWeight: 'medium'
                      }}
                    >
                      {user.avgResponseTime ? Math.round(user.avgResponseTime) : 0}ms
                    </Typography>
                  </TableCell>
                  
                  {detailed && (
                    <TableCell align="center">
                      <Chip
                        label={user.failedCommands || 0}
                        size="small"
                        color="error"
                        variant="outlined"
                      />
                    </TableCell>
                  )}
                  
                  <TableCell align="center">
                    <Typography variant="body2" color="text.secondary">
                      {user.lastActivity 
                        ? formatDistanceToNow(new Date(user.lastActivity), { addSuffix: true })
                        : 'Never'
                      }
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        {data.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              No users found for the selected period
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}
