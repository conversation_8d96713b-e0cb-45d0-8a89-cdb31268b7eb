#!/usr/bin/env node

/**
 * MQTT Connection Test Script
 * 
 * This script tests MQTT connectivity to help debug connection issues
 */

const mqtt = require('mqtt');

// Configuration
const BROKER_HOST = 'elec.mn';
const BROKER_PORT = 1883;
const BROKER_WS_PORT = 8084;
const TEST_DEVICE = 'test_device_123';

console.log('🔧 MQTT Connection Test Script');
console.log('================================');

// Test 1: TCP Connection
console.log('\n📡 Testing TCP Connection...');
const tcpUrl = `mqtt://${BROKER_HOST}:${BROKER_PORT}`;
console.log(`Connecting to: ${tcpUrl}`);

const tcpClient = mqtt.connect(tcpUrl, {
  clientId: `test_tcp_${Math.random().toString(16).substr(2, 8)}`,
  clean: true,
  keepalive: 60,
  connectTimeout: 10000,
  reconnectPeriod: 0 // Disable auto-reconnect for testing
});

tcpClient.on('connect', () => {
  console.log('✅ TCP Connection: SUCCESS');
  
  // Test publishing
  const testTopic = `${TEST_DEVICE}`;
  const testMessage = JSON.stringify({ command: 'test', timestamp: Date.now() });
  
  tcpClient.publish(testTopic, testMessage, (err) => {
    if (err) {
      console.log('❌ TCP Publish: FAILED', err.message);
    } else {
      console.log('✅ TCP Publish: SUCCESS');
    }
    
    tcpClient.end();
    testWebSocketConnection();
  });
});

tcpClient.on('error', (error) => {
  console.log('❌ TCP Connection: FAILED');
  console.log('Error:', error.message);
  tcpClient.end();
  testWebSocketConnection();
});

tcpClient.on('offline', () => {
  console.log('⚠️  TCP Connection: OFFLINE');
});

// Test 2: WebSocket Connection
function testWebSocketConnection() {
  console.log('\n🌐 Testing WebSocket Connection...');
  const wsUrl = `ws://${BROKER_HOST}:${BROKER_WS_PORT}/mqtt`;
  console.log(`Connecting to: ${wsUrl}`);

  const wsClient = mqtt.connect(wsUrl, {
    clientId: `test_ws_${Math.random().toString(16).substr(2, 8)}`,
    clean: true,
    keepalive: 60,
    connectTimeout: 10000,
    reconnectPeriod: 0
  });

  wsClient.on('connect', () => {
    console.log('✅ WebSocket Connection: SUCCESS');
    
    // Test subscribing
    const responseTopic = `${TEST_DEVICE}/msg`;
    wsClient.subscribe(responseTopic, (err) => {
      if (err) {
        console.log('❌ WebSocket Subscribe: FAILED', err.message);
      } else {
        console.log('✅ WebSocket Subscribe: SUCCESS');
      }
      
      wsClient.end();
      testSecureWebSocketConnection();
    });
  });

  wsClient.on('error', (error) => {
    console.log('❌ WebSocket Connection: FAILED');
    console.log('Error:', error.message);
    wsClient.end();
    testSecureWebSocketConnection();
  });

  wsClient.on('offline', () => {
    console.log('⚠️  WebSocket Connection: OFFLINE');
  });
}

// Test 3: Secure WebSocket Connection (WSS)
function testSecureWebSocketConnection() {
  console.log('\n🔒 Testing Secure WebSocket Connection...');
  const wssUrl = `wss://${BROKER_HOST}:${BROKER_WS_PORT}/mqtt`;
  console.log(`Connecting to: ${wssUrl}`);

  const wssClient = mqtt.connect(wssUrl, {
    clientId: `test_wss_${Math.random().toString(16).substr(2, 8)}`,
    clean: true,
    keepalive: 60,
    connectTimeout: 10000,
    reconnectPeriod: 0,
    rejectUnauthorized: false // Allow self-signed certificates for testing
  });

  wssClient.on('connect', () => {
    console.log('✅ Secure WebSocket Connection: SUCCESS');
    
    // Test full round-trip
    const testTopic = `${TEST_DEVICE}`;
    const responseTopic = `${TEST_DEVICE}/msg`;
    
    // Subscribe to response topic first
    wssClient.subscribe(responseTopic, (err) => {
      if (err) {
        console.log('❌ WSS Subscribe: FAILED', err.message);
        wssClient.end();
        printSummary();
        return;
      }
      
      console.log('✅ WSS Subscribe: SUCCESS');
      
      // Set up message handler
      wssClient.on('message', (topic, message) => {
        if (topic === responseTopic) {
          console.log('✅ WSS Message Received: SUCCESS');
          console.log(`   Topic: ${topic}`);
          console.log(`   Message: ${message.toString()}`);
        }
      });
      
      // Send test command
      const testMessage = JSON.stringify({ 
        command: 'check', 
        timestamp: Date.now(),
        test: true 
      });
      
      wssClient.publish(testTopic, testMessage, (err) => {
        if (err) {
          console.log('❌ WSS Publish: FAILED', err.message);
        } else {
          console.log('✅ WSS Publish: SUCCESS');
        }
        
        // Wait a bit for potential response, then cleanup
        setTimeout(() => {
          wssClient.end();
          printSummary();
        }, 3000);
      });
    });
  });

  wssClient.on('error', (error) => {
    console.log('❌ Secure WebSocket Connection: FAILED');
    console.log('Error:', error.message);
    wssClient.end();
    printSummary();
  });

  wssClient.on('offline', () => {
    console.log('⚠️  Secure WebSocket Connection: OFFLINE');
  });
}

function printSummary() {
  console.log('\n📋 Test Summary');
  console.log('================');
  console.log('If you see connection failures:');
  console.log('1. Check if the MQTT broker is running');
  console.log('2. Verify firewall settings');
  console.log('3. Check network connectivity');
  console.log('4. Verify broker configuration');
  console.log('\nFor browser connections, use WSS (Secure WebSocket)');
  console.log('For server connections, use TCP or WS');
  console.log('\n🏁 Test completed!');
}

// Handle script termination
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Test interrupted by user');
  process.exit(0);
});

// Set overall timeout
setTimeout(() => {
  console.log('\n⏰ Test timeout reached');
  printSummary();
  process.exit(0);
}, 30000);
